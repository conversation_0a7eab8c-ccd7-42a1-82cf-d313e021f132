# FastAPI Production Dependencies
# Core dependencies required to run the application

# FastAPI Core Framework
fastapi[standard]>=0.100.0

# Database
sqlalchemy>=2.0.0
asyncpg>=0.28.0  # PostgreSQL async driver
psycopg2-binary>=2.9.0  # PostgreSQL sync driver (backup)
alembic>=1.12.0  # Database migration

# Supabase Support
supabase>=2.0.0

# Authentication and Security
python-jose[cryptography]>=3.3.0  # JWT handling
passlib[bcrypt]>=1.7.4  # Password encryption
bcrypt>=3.2.0,<4.0.0  # Password encryption lib (compatible with passlib 1.7.4)
python-multipart>=0.0.6  # Form data processing

# Configuration Management
pydantic-settings>=2.0.0
python-dotenv>=1.0.0

# Email Support
email-validator>=2.0.0
aiosmtplib>=3.0.0  # Async email sending

# HTTP Client
httpx>=0.24.0  # For WeChat Pay API and general HTTP requests

# Type Checking
typing-extensions>=4.5.0
