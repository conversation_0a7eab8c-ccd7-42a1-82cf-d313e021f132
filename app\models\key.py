"""
秘钥相关数据模型
"""

import enum
from datetime import datetime
from typing import TYPE_CHECKING, Optional

from sqlalchemy import DECIMAL, DateTime, Enum, ForeignKey, Integer, String
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.core.datetime_utils import default_datetime
from app.db.database import Base

if TYPE_CHECKING:
    from .product import Product
    from .user import User


class KeyStatus(enum.Enum):
    """
    秘钥状态枚举
    统一规范：常量名和值都使用大写，确保数据库和代码的一致性
    """

    ACTIVE = "ACTIVE"  # 活跃秘钥
    EXPIRED = "EXPIRED"  # 过期秘钥
    DISABLED = "DISABLED"  # 禁用秘钥


class Key(Base):
    """秘钥表 - 根据数据库设计规范"""

    __tablename__ = "keys"

    key_id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    user_id: Mapped[int] = mapped_column(
        Integer, ForeignKey("users.user_id"), nullable=False, index=True
    )
    product_id: Mapped[Optional[int]] = mapped_column(
        Integer, ForeignKey("products.product_id"), index=True
    )
    key_string: Mapped[str] = mapped_column(
        String(255), unique=True, nullable=False, index=True
    )
    price: Mapped[float] = mapped_column(DECIMAL(10, 2), nullable=False)
    start_date: Mapped[datetime] = mapped_column(DateTime, nullable=False)
    end_date: Mapped[datetime] = mapped_column(DateTime, nullable=False)
    activated_count: Mapped[int] = mapped_column(Integer, default=0)
    max_activation_count: Mapped[int] = mapped_column(Integer, nullable=False)
    status: Mapped[KeyStatus] = mapped_column(Enum(KeyStatus), default=KeyStatus.ACTIVE)
    created_date: Mapped[datetime] = mapped_column(DateTime, default=default_datetime)
    updated_date: Mapped[datetime] = mapped_column(
        DateTime, default=default_datetime, onupdate=default_datetime
    )

    # 关系
    user: Mapped["User"] = relationship("User", back_populates="keys")
    product: Mapped[Optional["Product"]] = relationship(
        "Product", back_populates="keys"
    )
    key_activations: Mapped[list["KeyActivation"]] = relationship(
        "KeyActivation", back_populates="key"
    )


class KeyActivation(Base):
    """秘钥激活记录表 - 根据数据库设计规范"""

    __tablename__ = "key_activations"

    activation_id: Mapped[int] = mapped_column(
        Integer, primary_key=True, autoincrement=True
    )
    key_id: Mapped[int] = mapped_column(
        Integer, ForeignKey("keys.key_id"), nullable=False, index=True
    )
    user_id: Mapped[int] = mapped_column(
        Integer, ForeignKey("users.user_id"), nullable=False
    )
    device_id: Mapped[Optional[str]] = mapped_column(String(255))
    ip_address: Mapped[Optional[str]] = mapped_column(String(45))
    activation_date: Mapped[datetime] = mapped_column(
        DateTime, default=default_datetime, index=True
    )

    # 关系
    key: Mapped["Key"] = relationship("Key", back_populates="key_activations")
    user: Mapped["User"] = relationship("User", back_populates="key_activations")
