# This CITATION.cff file was generated with cffinit.
# Visit https://bit.ly/cffinit to generate yours today!

cff-version: 1.2.0
title: FastAPI
message: >-
  If you use this software, please cite it using the
  metadata from this file.
type: software
authors:
  - given-names: <PERSON><PERSON><PERSON><PERSON>
    family-names: <PERSON>
    email: tian<PERSON><PERSON>@gmail.com
identifiers:
repository-code: 'https://github.com/fastapi/fastapi'
url: 'https://fastapi.tiangolo.com'
abstract: >-
  FastAPI framework, high performance, easy to learn, fast to code,
  ready for production
keywords:
  - fastapi
  - pydantic
  - starlette
license: MIT
