"""
产品相关API路由
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.security import get_current_active_user
from app.db.database import get_db
from app.models.product import Product
from app.models.user import User
from app.schemas.product import ProductResponse

router = APIRouter()


@router.get("/", response_model=list[ProductResponse], operation_id="products_list")
async def get_products(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """获取所有产品列表（需要认证）"""
    try:
        result = await db.execute(select(Product))
        products = result.scalars().all()
        return [ProductResponse.model_validate(product) for product in products]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get product list: {str(e)}",
        )
