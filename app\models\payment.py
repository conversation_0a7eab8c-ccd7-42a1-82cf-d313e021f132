"""
支付记录相关数据模型
"""

import enum
from datetime import datetime
from typing import TYPE_CHECKING, Optional

from sqlalchemy import DECIMAL, DateTime, Enum, ForeignKey, Integer, String
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.core.datetime_utils import default_datetime
from app.db.database import Base

if TYPE_CHECKING:
    from .product import Product
    from .user import User
    from .wechat_order import WeChatOrder


class PaymentType(enum.Enum):
    """
    支付类型枚举
    统一规范：常量名和值都使用大写，确保数据库和代码的一致性
    """

    PURCHASE = "PURCHASE"  # 购买
    REFUND = "REFUND"  # 退款


class PaymentRecord(Base):
    """支付记录表 - 根据数据库设计规范"""

    __tablename__ = "payment_records"

    payment_id: Mapped[int] = mapped_column(
        Integer, primary_key=True, autoincrement=True
    )
    user_id: Mapped[int] = mapped_column(
        Integer, ForeignKey("users.user_id"), nullable=False, index=True
    )
    amount: Mapped[float] = mapped_column(DECIMAL(10, 2), nullable=False)
    payment_type: Mapped[PaymentType] = mapped_column(Enum(PaymentType), nullable=False)
    payment_date: Mapped[datetime] = mapped_column(
        DateTime, default=default_datetime, index=True
    )
    product_id: Mapped[Optional[int]] = mapped_column(
        Integer, ForeignKey("products.product_id")
    )
    order_id: Mapped[Optional[str]] = mapped_column(String(50))
    payment_method: Mapped[Optional[str]] = mapped_column(String(50))

    # 关系
    user: Mapped["User"] = relationship("User", back_populates="payment_records")
    product: Mapped[Optional["Product"]] = relationship(
        "Product", back_populates="payment_records"
    )
    wechat_order: Mapped[Optional["WeChatOrder"]] = relationship(
        "WeChatOrder", back_populates="payment_record"
    )
