"""
应用配置管理
"""

import os
from pathlib import Path
from typing import List

from dotenv import load_dotenv
from pydantic_settings import BaseSettings

# 加载环境变量
load_dotenv()


def get_app_version() -> str:
    """
    获取应用版本号，按优先级顺序：
    1. pyproject.toml 文件
    2. VERSION 文件
    3. 默认版本
    """
    project_root = Path(__file__).parent.parent.parent

    # 1. 从 pyproject.toml 读取
    try:
        pyproject_path = project_root / "pyproject.toml"
        if pyproject_path.exists():
            import tomllib

            with open(pyproject_path, "rb") as f:
                data = tomllib.load(f)
            version = data.get("project", {}).get("version")
            if version:
                return version.strip()
    except Exception:
        pass

    # 2. 从 VERSION 文件读取
    try:
        version_file = project_root / "VERSION"
        if version_file.exists():
            return version_file.read_text().strip()
    except Exception:
        pass

    # 3. 默认版本
    return "1.1.0"


class Settings(BaseSettings):
    """应用配置类"""

    # 基础配置
    APP_NAME: str = "Fast MetaTrader Auth"
    APP_VERSION: str = get_app_version()
    DEBUG_LEVEL: int = int(os.getenv("DEBUG_LEVEL", "1"))
    IS_DEVELOPMENT: bool = os.getenv("IS_DEVELOPMENT", "true").lower() == "true"
    SHOW_DOCS_INFO: bool = (
        os.getenv("SHOW_DOCS_INFO", "true").lower() == "true"
    )  # 是否显示API文档页面（/docs, /redoc, /openapi.json）

    # 为了向后兼容，提供 DEBUG 属性
    @property
    def DEBUG(self) -> bool:
        """向后兼容的 DEBUG 属性 - DEBUG_LEVEL > 0 时为 True"""
        return self.DEBUG_LEVEL > 0

    # 为了兼容性，提供环境名称属性
    @property
    def environment_name(self) -> str:
        """返回环境名称字符串"""
        return "development" if self.IS_DEVELOPMENT else "production"

    # API配置
    API_BASE_STR: str = "/api"

    # CORS配置
    ALLOWED_ORIGINS: List[str] = os.getenv("ALLOWED_ORIGINS", "*").split(",")

    # 数据库配置 - 优先使用非池化连接避免pgbouncer问题
    DATABASE_URL: str = (
        os.getenv("DATABASE_URL", "")
        or os.getenv("POSTGRES_URL_NON_POOLING", "")  # 优先使用直接连接
        or os.getenv("POSTGRES_URL", "")              # 备用pgbouncer连接
    )

    # 备用独立数据库变量配置（可选）
    DB_TYPE: str = os.getenv("DB_TYPE", "")  # postgresql, mysql, sqlite
    DB_HOST: str = os.getenv("DB_HOST", "")
    DB_PORT: int = int(os.getenv("DB_PORT", "5432"))
    DB_USER: str = os.getenv("DB_USER", "")
    DB_PASSWORD: str = os.getenv("DB_PASSWORD", "")
    DB_NAME: str = os.getenv("DB_NAME", "")

    # 数据库类型自动检测
    @property
    def database_type(self) -> str:
        """根据配置自动检测数据库类型"""
        # 优先使用独立变量中的 DB_TYPE
        if self.DB_TYPE:
            return self.DB_TYPE.lower()

        # 其次从 DATABASE_URL 检测
        database_url = self.get_effective_database_url()
        if not database_url:
            return "none"
        if "postgresql" in database_url:
            return "postgresql"
        elif "mysql" in database_url:
            return "mysql"
        elif "sqlite" in database_url:
            return "sqlite"
        else:
            return "unknown"

    # Supabase配置 - 支持 Vercel 自动配置的所有变量
    SUPABASE_URL: str = os.getenv("SUPABASE_URL", "") or os.getenv(
        "NEXT_PUBLIC_SUPABASE_URL", ""
    )
    SUPABASE_ANON_KEY: str = os.getenv("SUPABASE_ANON_KEY", "") or os.getenv(
        "NEXT_PUBLIC_SUPABASE_ANON_KEY", ""
    )
    SUPABASE_SERVICE_ROLE_KEY: str = os.getenv("SUPABASE_SERVICE_ROLE_KEY", "")
    SUPABASE_JWT_SECRET: str = os.getenv("SUPABASE_JWT_SECRET", "")

    # PostgreSQL 连接详情 (Vercel 自动配置)
    POSTGRES_HOST: str = os.getenv("POSTGRES_HOST", "")
    POSTGRES_USER: str = os.getenv("POSTGRES_USER", "")
    POSTGRES_PASSWORD: str = os.getenv("POSTGRES_PASSWORD", "")
    POSTGRES_DATABASE: str = os.getenv("POSTGRES_DATABASE", "")

    def get_effective_database_url(self) -> str:
        """获取有效的数据库 URL，支持多种来源"""
        # 优先级1：独立数据库变量构建
        if all(
            [self.DB_TYPE, self.DB_HOST, self.DB_USER, self.DB_PASSWORD, self.DB_NAME]
        ):
            return self._build_database_url_from_components()

        # 优先级2：直接的 DATABASE_URL
        if self.DATABASE_URL:
            return self.DATABASE_URL

        # 优先级3：优先使用非池化连接，避免pgbouncer问题
        postgres_url_non_pooling = os.getenv("POSTGRES_URL_NON_POOLING")
        if postgres_url_non_pooling:
            return postgres_url_non_pooling

        # 备用：使用池化连接（pgbouncer）
        postgres_url = os.getenv("POSTGRES_URL")
        if postgres_url:
            return postgres_url

        return ""

    def _build_database_url_from_components(self) -> str:
        """从独立的数据库变量构建数据库 URL"""
        db_type = self.DB_TYPE.lower()

        # 根据数据库类型构建 URL
        if db_type == "postgresql":
            return f"postgresql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"
        elif db_type == "mysql":
            return f"mysql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"
        elif db_type == "sqlite":
            # SQLite 不需要用户名、密码、主机和端口
            return f"sqlite:///{self.DB_NAME}"
        else:
            # 默认使用 PostgreSQL 格式
            return f"postgresql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"

    def get_database_config_info(self) -> dict:
        """获取数据库配置信息（用于调试）"""
        return {
            "config_source": self._get_database_config_source(),
            "database_type": self.database_type,
            "db_components": {
                "DB_TYPE": self.DB_TYPE,
                "DB_HOST": self.DB_HOST,
                "DB_PORT": self.DB_PORT,
                "DB_USER": self.DB_USER,
                "DB_NAME": self.DB_NAME,
                "DB_PASSWORD": "***" if self.DB_PASSWORD else "",
            },
            "has_database_url": bool(self.DATABASE_URL),
        }

    def _get_database_config_source(self) -> str:
        """获取数据库配置来源"""
        if all(
            [self.DB_TYPE, self.DB_HOST, self.DB_USER, self.DB_PASSWORD, self.DB_NAME]
        ):
            return "independent_variables"
        elif self.DATABASE_URL:
            return "database_url"
        else:
            return "none"

    # JWT配置
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
    JWT_ALGORITHM: str = os.getenv("JWT_ALGORITHM", "HS256")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("JWT_EXPIRE_MINUTES", "30"))

    # 密码加密配置
    BCRYPT_ROUNDS: int = int(os.getenv("BCRYPT_ROUNDS", "12"))

    # 超级管理员配置（环境变量存储，应急访问）
    SUPER_ADMIN_EMAIL: str = os.getenv("ADMIN_EMAIL", "<EMAIL>")
    SUPER_ADMIN_PASSWORD: str = os.getenv("ADMIN_PASSWORD", "admin123")

    # 分页配置
    DEFAULT_PAGE_SIZE: int = 20
    MAX_PAGE_SIZE: int = 100

    # 速率限制配置
    RATE_LIMIT_REQUESTS: int = 100  # 一般API请求限制：每小时100次
    RATE_LIMIT_WINDOW: int = 3600  # 速率限制时间窗口：1小时（3600秒）

    # 认证相关速率限制配置 - 防止恶意攻击
    REGISTRATION_RATE_LIMIT: int = int(
        os.getenv("REGISTRATION_RATE_LIMIT", "10")
    )  # 每IP每小时最多注册次数
    LOGIN_RATE_LIMIT: int = int(
        os.getenv("LOGIN_RATE_LIMIT", "50")
    )  # 每IP每小时最多登录次数
    TOKEN_RATE_LIMIT: int = int(
        os.getenv("TOKEN_RATE_LIMIT", "50")
    )  # 每IP每小时最多令牌获取次数
    VERIFY_EMAIL_RATE_LIMIT: int = int(
        os.getenv("VERIFY_EMAIL_RATE_LIMIT", "10")
    )  # 每IP每小时最多邮箱验证次数
    RESEND_VERIFICATION_RATE_LIMIT: int = int(
        os.getenv("RESEND_VERIFICATION_RATE_LIMIT", "10")
    )  # 每IP每小时最多重发验证码次数
    AUTH_RATE_LIMIT_WINDOW: int = int(
        os.getenv("AUTH_RATE_LIMIT_WINDOW", "3600")
    )  # 认证相关速率限制时间窗口（秒）
    REQUIRE_EMAIL_VERIFICATION: bool = (
        os.getenv("REQUIRE_EMAIL_VERIFICATION", "false").lower() == "true"
    )  # 邮箱验证，=true的时候注册后即可登录，无需验证

    # 邮件服务配置
    SMTP_HOST: str = os.getenv("SMTP_HOST", "")
    SMTP_PORT: int = int(os.getenv("SMTP_PORT", "587"))
    SMTP_USERNAME: str = os.getenv("SMTP_USER", "")  # 使用 SMTP_USER
    SMTP_PASSWORD: str = os.getenv("SMTP_PASSWORD", "")
    SMTP_USE_TLS: bool = (
        os.getenv("SMTP_TLS", "true").lower() == "true"
    )  # 使用 SMTP_TLS
    SMTP_USE_SSL: bool = (
        os.getenv("SMTP_SSL", "false").lower() == "true"
    )  # 使用 SMTP_SSL
    EMAIL_FROM: str = os.getenv("EMAILS_FROM_EMAIL", "")  # 使用 EMAILS_FROM_EMAIL
    EMAIL_FROM_NAME: str = os.getenv(
        "MAIL_FROM_NAME", "FastMetaAuth"
    )  # 使用 MAIL_FROM_NAME

    # 密码强度要求 - 提高账户安全性
    MIN_PASSWORD_LENGTH: int = int(
        os.getenv("MIN_PASSWORD_LENGTH", "8")
    )  # 最小密码长度要求
    REQUIRE_STRONG_PASSWORD: bool = (
        os.getenv("REQUIRE_STRONG_PASSWORD", "true").lower() == "true"
    )  # 是否要求强密码（大小写+数字+特殊字符）

    # 开发模式配置
    DEVELOPMENT_MODE: bool = (
        os.getenv("DEVELOPMENT_MODE", "false").lower() == "true"
    )  # 开发模式开关，启用额外调试功能

    # 微信支付配置
    WECHAT_APPID: str = os.getenv("WECHAT_APPID", "")
    WECHAT_MCHID: str = os.getenv("WECHAT_MCHID", "")
    WECHAT_APIV2_KEY: str = os.getenv("WECHAT_APIV2_KEY", "")
    WECHAT_APP_SECRET: str = os.getenv("WECHAT_APP_SECRET", "")
    WECHAT_NOTIFY_URL: str = os.getenv("WECHAT_NOTIFY_URL", "")
    WECHAT_CERT_PATH: str = os.getenv("WECHAT_CERT_PATH", "")  # 微信支付证书路径
    WECHAT_KEY_PATH: str = os.getenv("WECHAT_KEY_PATH", "")  # 微信支付私钥路径

    def validate_wechat_config(self) -> bool:
        """验证微信支付配置是否完整"""
        required_fields = [
            self.WECHAT_APPID,
            self.WECHAT_MCHID,
            self.WECHAT_APIV2_KEY,
            self.WECHAT_NOTIFY_URL,
        ]
        return all(field for field in required_fields)

    # 用户访问模式控制 - 核心安全设置
    # 支持三种预设状态：
    # - "1" 或 "open": 开放模式 - 允许用户注册和登录
    # - "2" 或 "login_only": 仅登录模式 - 允许登录，禁止注册
    # - "3" 或 "restricted": 限制模式 - 仅管理员可访问，禁用用户注册和登录
    _USER_ACCESS_MODE_RAW: str = os.getenv("USER_ACCESS_MODE", "1")

    @property
    def USER_ACCESS_MODE(self) -> str:
        """获取标准化的访问模式"""
        mode_mapping = {
            "1": "open",
            "2": "login_only",
            "3": "restricted",
            "open": "open",
            "login_only": "login_only",
            "restricted": "restricted",
        }
        return mode_mapping.get(self._USER_ACCESS_MODE_RAW.lower(), "restricted")

    # 向后兼容的属性
    @property
    def ADMIN_ONLY_MODE(self) -> bool:
        """向后兼容：当模式为 restricted 时返回 True"""
        return self.USER_ACCESS_MODE == "restricted"

    @property
    def ENABLE_USER_REGISTRATION(self) -> bool:
        """向后兼容：当模式为 open 时返回 True"""
        return self.USER_ACCESS_MODE == "open"

    # 新的便捷属性
    @property
    def allow_user_registration(self) -> bool:
        """是否允许用户注册"""
        return self.USER_ACCESS_MODE == "open"

    @property
    def allow_user_login(self) -> bool:
        """是否允许用户登录"""
        return self.USER_ACCESS_MODE in ["open", "login_only"]

    @property
    def is_restricted_mode(self) -> bool:
        """是否为限制模式（仅管理员）"""
        return self.USER_ACCESS_MODE == "restricted"

    def get_access_mode_description(self) -> str:
        """获取访问模式的简单描述"""
        return self.get_access_mode_number()

    def get_access_mode_number(self) -> str:
        """获取访问模式对应的数字"""
        mode_numbers = {"open": "1", "login_only": "2", "restricted": "3"}
        return mode_numbers.get(self.USER_ACCESS_MODE, "3")

    def validate_access_mode(self) -> bool:
        """验证访问模式是否有效"""
        valid_modes = ["open", "login_only", "restricted"]
        return self.USER_ACCESS_MODE in valid_modes

    def set_access_mode(self, mode: str) -> bool:
        """动态设置访问模式（运行时修改）"""
        # 验证模式是否有效
        valid_inputs = ["1", "2", "3", "open", "login_only", "restricted"]
        if mode not in valid_inputs:
            return False

        # 更新内部变量
        self._USER_ACCESS_MODE_RAW = mode
        return True

    def get_all_access_modes(self) -> dict:
        """获取所有可用的访问模式信息"""
        return {
            "1": {
                "name": "open",
                "number": "1",
                "user_registration": True,
                "user_login": True,
            },
            "2": {
                "name": "login_only",
                "number": "2",
                "user_registration": False,
                "user_login": True,
            },
            "3": {
                "name": "restricted",
                "number": "3",
                "user_registration": False,
                "user_login": False,
            },
        }

    class Config:
        case_sensitive = True
        env_file = ".env"
        extra = "ignore"  # 忽略额外的环境变量，避免验证错误


# 创建全局配置实例
settings = Settings()
