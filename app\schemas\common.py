"""
通用Pydantic模式定义
"""

from datetime import datetime
from enum import Enum
from typing import List, Optional

from pydantic import BaseModel, Field


# 枚举类型 - 统一规范：常量名和值都使用大写
class UserStatus(str, Enum):
    """
    用户状态枚举
    统一规范：常量名和值都使用大写，确保数据库和代码的一致性
    """

    ACTIVE = "ACTIVE"  # 活跃用户
    BANNED = "BANNED"  # 被封禁用户
    PENDING = "PENDING"  # 待激活用户


class UserRole(str, Enum):
    """
    用户角色枚举
    统一规范：常量名和值都使用大写，确保数据库和代码的一致性
    """

    USER = "USER"  # 普通用户
    ADMIN = "ADMIN"  # 管理员
    SUPER_ADMIN = "SUPER_ADMIN"  # 超级管理员


class KeyStatus(str, Enum):
    """
    秘钥状态枚举
    统一规范：常量名和值都使用大写，确保数据库和代码的一致性
    """

    ACTIVE = "ACTIVE"  # 活跃秘钥
    EXPIRED = "EXPIRED"  # 过期秘钥
    DISABLED = "DISABLED"  # 禁用秘钥


class PaymentType(str, Enum):
    """
    支付类型枚举
    统一规范：常量名和值都使用大写，确保数据库和代码的一致性
    """

    PURCHASE = "PURCHASE"  # 购买
    REFUND = "REFUND"  # 退款


# 基础响应模式
class BaseResponse(BaseModel):
    """基础响应模式"""

    success: bool
    message: str


class HealthResponse(BaseModel):
    """健康检查响应"""

    status: str
    message: str
    version: str
    database_connected: bool = False
    supabase_connected: bool = False


# 认证相关模式
class Token(BaseModel):
    """令牌模式"""

    access_token: str
    token_type: str = "bearer"
    expires_in: int


class TokenData(BaseModel):
    """令牌数据模式"""

    username: Optional[str] = None
    user_id: Optional[int] = None


class AuthResponse(BaseResponse):
    """认证响应模式"""

    token: Optional[Token] = None
    user: Optional[dict] = None


# 分页相关模式
class PaginationParams(BaseModel):
    """分页参数模式"""

    page: int = Field(default=1, ge=1)
    size: int = Field(default=20, ge=1, le=100)


class PaginatedResponse(BaseModel):
    """分页响应模式"""

    items: List[dict]
    total: int
    page: int
    size: int
    pages: int


# 服务状态管理相关模式
class AccessMode(str, Enum):
    """
    访问模式枚举
    """

    OPEN = "open"  # 开放模式 - 允许用户注册和登录
    LOGIN_ONLY = "login_only"  # 仅登录模式 - 允许登录，禁止注册
    RESTRICTED = "restricted"  # 限制模式 - 仅管理员可访问


class ServiceStatusRequest(BaseModel):
    """服务状态设置请求"""

    access_mode: str = Field(
        ..., description="访问模式：1/open, 2/login_only, 3/restricted"
    )


class ServiceStatusResponse(BaseModel):
    """服务状态响应"""

    success: bool
    message: str
    current_mode: str
    current_mode_number: str
    current_mode_description: str
    user_registration_allowed: bool
    user_login_allowed: bool
    timestamp: datetime


class ServiceStatusInfo(BaseModel):
    """服务状态信息"""

    current_mode: str
    current_mode_number: str
    current_mode_description: str
    user_registration_allowed: bool
    user_login_allowed: bool
    available_modes: dict
    timestamp: datetime
