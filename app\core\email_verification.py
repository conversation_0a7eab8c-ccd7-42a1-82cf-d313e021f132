"""
邮箱验证相关工具函数
"""

import random
import string
from datetime import datetime, timedelta
from typing import Optional

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.email_service import email_service
from app.models.user import User


def generate_verification_code() -> str:
    """生成6位数字验证码"""
    return "".join(random.choices(string.digits, k=6))


def get_verification_code_expiry() -> datetime:
    """获取验证码过期时间（15分钟后）"""
    return datetime.now() + timedelta(minutes=15)


async def create_verification_code(
    db: AsyncSession, user: User, send_email: bool = True
) -> str:
    """
    为用户创建验证码并发送邮件

    Args:
        db: 数据库会话
        user: 用户对象
        send_email: 是否发送邮件（默认True）

    Returns:
        str: 验证码
    """
    verification_code = generate_verification_code()
    expiry_time = get_verification_code_expiry()

    # 更新用户的验证码信息
    user.email_verification_code = verification_code
    user.email_verification_expires = expiry_time

    await db.commit()
    await db.refresh(user)

    # 发送验证码邮件 - 真正的异步后台发送
    if send_email:
        import asyncio

        async def send_email_background():
            """后台异步发送邮件"""
            try:
                success = await email_service.send_verification_email(
                    user.email, verification_code
                )
                if success:
                    print(f"✅ 验证码邮件已发送到: {user.email}")
                else:
                    print(f"❌ 验证码邮件发送失败: {user.email}")
                    # 邮件发送失败时，仍然显示验证码到终端作为备用
                    print("\n" + "=" * 60)
                    print("🔥🔥🔥 邮件发送失败，验证码备用显示 🔥🔥🔥")
                    print("=" * 60)
                    print(f"📧 收件人: {user.email}")
                    print(f"🔢 验证码: {verification_code}")
                    print("⏰ 有效期: 15分钟")
                    print("=" * 60)
                    print("请使用此验证码调用 /api/auth/verify-email 接口")
                    print("=" * 60 + "\n")
            except Exception as e:
                print(f"❌ 邮件发送异常: {str(e)}")
                # 异常时显示验证码到终端
                print("\n" + "=" * 60)
                print("🔥🔥🔥 邮件发送异常，验证码备用显示 🔥🔥🔥")
                print("=" * 60)
                print(f"📧 收件人: {user.email}")
                print(f"🔢 验证码: {verification_code}")
                print("⏰ 有效期: 15分钟")
                print("=" * 60)
                print("请使用此验证码调用 /api/auth/verify-email 接口")
                print("=" * 60 + "\n")

        # 创建后台任务，不等待完成
        asyncio.create_task(send_email_background())
        print(f"📤 验证码邮件已加入发送队列: {user.email}")

    return verification_code


async def verify_email_code(db: AsyncSession, email: str, code: str) -> bool:
    """验证邮箱验证码"""
    # 查找用户
    result = await db.execute(select(User).where(User.email == email))
    user = result.scalar_one_or_none()

    if not user:
        return False

    # 检查验证码是否存在
    if not user.email_verification_code:
        return False

    # 检查验证码是否过期
    if (
        user.email_verification_expires
        and user.email_verification_expires < datetime.now()
    ):
        # 清除过期的验证码
        user.email_verification_code = None
        user.email_verification_expires = None
        await db.commit()
        return False

    # 验证码匹配
    if user.email_verification_code == code:
        # 验证成功，激活用户并清除验证码
        user.email_verified = True
        user.status = (
            user.status if user.status != user.status.PENDING else user.status.ACTIVE
        )
        user.email_verification_code = None
        user.email_verification_expires = None
        await db.commit()
        return True

    return False


async def resend_verification_code(db: AsyncSession, email: str) -> Optional[str]:
    """重新发送验证码"""
    # 查找用户
    result = await db.execute(select(User).where(User.email == email))
    user = result.scalar_one_or_none()

    if not user:
        return None

    # 如果用户已经验证过邮箱，不需要重新发送
    if user.email_verified:
        return None

    # 生成新的验证码并发送邮件
    return await create_verification_code(db, user, send_email=True)


def get_verification_email_template() -> str:
    """获取验证码邮件模板"""
    return """
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center;">
            <h2 style="color: #333; margin-bottom: 20px;">邮箱验证</h2>

            <p style="color: #666; font-size: 16px; margin-bottom: 30px;">
                尊敬的用户，感谢您注册 {app_name}。
            </p>

            <p style="color: #666; font-size: 16px; margin-bottom: 20px;">
                您的邮箱验证码是：
            </p>

            <div style="background-color: #007bff; color: white; font-size: 32px; font-weight: bold;
                        padding: 20px; border-radius: 8px; letter-spacing: 8px; margin: 20px 0;">
                {verification_code}
            </div>

            <p style="color: #999; font-size: 14px; margin-top: 20px;">
                验证码有效期为15分钟，请及时使用。
            </p>

            <p style="color: #999; font-size: 14px;">
                如果您没有注册账号，请忽略此邮件。
            </p>

            <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">

            <p style="color: #999; font-size: 12px;">
                此邮件由系统自动发送，请勿直接回复。<br>
                如有问题，请联系：{support_email}
            </p>
        </div>
    </div>
    """
