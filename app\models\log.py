"""
日志相关数据模型
"""

from datetime import datetime
from typing import TYPE_CHECKING, Optional

from sqlalchemy import JSO<PERSON>, DateTime, Foreign<PERSON>ey, Integer, String
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.db.database import Base

if TYPE_CHECKING:
    from .user import User


class UserLog(Base):
    """用户操作日志表 - 根据数据库设计规范"""

    __tablename__ = "user_logs"

    log_id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    user_id: Mapped[int] = mapped_column(
        Integer, ForeignKey("users.user_id"), nullable=False, index=True
    )
    action: Mapped[str] = mapped_column(String(50), nullable=False)
    action_date: Mapped[datetime] = mapped_column(
        DateTime, default=lambda: datetime.utcnow(), index=True
    )
    details: Mapped[Optional[str]] = mapped_column(JSON)  # JSON格式的操作详情

    # 关系
    user: Mapped["User"] = relationship("User", back_populates="user_logs")
