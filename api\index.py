"""
FastAPI软件授权系统 - Vercel部署版本
专为Vercel无服务器环境优化的完整软件授权管理系统
"""

import os
import sys
from pathlib import Path

# 设置环境变量确保正确的模块导入
os.environ.setdefault("PYTHONPATH", ".")

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 确保工作目录正确
os.chdir(project_root)

# 导入主应用实例
import_error_msg = None
try:
    from main import app

    # 确保应用实例正确
    if not callable(app):
        raise ImportError("App is not callable")

except Exception as e:
    # 如果导入失败，创建一个简单的错误应用
    import traceback

    from fastapi import FastAPI
    from fastapi.responses import JSONResponse

    import_error_msg = str(e)
    error_traceback = traceback.format_exc()

    app = FastAPI(
        title="FastAPI Import Error", description="主应用导入失败", version="1.0.0"
    )

    @app.get("/")
    async def import_error():
        return JSONResponse(
            status_code=500,
            content={
                "error": "Import Error",
                "message": f"无法导入主应用: {import_error_msg}",
                "traceback": error_traceback,
                "python_path": sys.path,
                "working_directory": str(os.getcwd()),
            },
        )

    @app.get("/health")
    async def health_error():
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": "Application failed to import",
                "error": import_error_msg,
            },
        )

# Vercel 兼容的处理器 - 直接导出应用实例
# Vercel 会自动处理 ASGI 应用
# 不需要自定义处理器函数

# 导出应用实例
__all__ = ["app"]
