"""
微信支付订单相关Pydantic模式定义
"""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict, Field

from app.models.wechat_order import WeChatTradeState, WeChatTradeType


# 微信支付订单相关模式
class WeChatOrderBase(BaseModel):
    """微信支付订单基础模式"""

    description: str = Field(..., max_length=128, description="商品描述")
    total: int = Field(..., gt=0, description="金额，单位为分")
    trade_type: WeChatTradeType = Field(..., description="支付方式")
    product_id: Optional[int] = Field(None, description="产品ID")
    openid: Optional[str] = Field(
        None, max_length=128, description="用户OpenID（JSAPI支付必填）"
    )


class WeChatOrderCreate(WeChatOrderBase):
    """微信支付订单创建模式"""

    # 自动生成的字段不需要在创建时提供
    pass


class WeChatOrderResponse(BaseModel):
    """微信支付订单响应模式"""

    model_config = ConfigDict(from_attributes=True)

    order_id: int
    user_id: int
    product_id: Optional[int]
    payment_record_id: Optional[int]
    out_trade_no: str
    transaction_id: Optional[str]
    prepay_id: Optional[str]
    description: str
    total: int
    trade_type: WeChatTradeType
    trade_state: WeChatTradeState
    code_url: Optional[str]
    h5_url: Optional[str]
    openid: Optional[str]
    created_at: datetime
    updated_at: datetime
    success_time: Optional[datetime]
    notify_received: bool
    notify_time: Optional[datetime]


class WeChatOrderQuery(BaseModel):
    """微信支付订单查询响应模式"""

    out_trade_no: str
    transaction_id: Optional[str]
    trade_state: WeChatTradeState
    trade_state_desc: str
    total: int
    success_time: Optional[str]


class WeChatOrderUpdate(BaseModel):
    """微信支付订单更新模式"""

    transaction_id: Optional[str] = None
    trade_state: Optional[WeChatTradeState] = None
    success_time: Optional[datetime] = None
    notify_received: Optional[bool] = None
    notify_time: Optional[datetime] = None


# 微信支付退款相关模式
class WeChatRefundBase(BaseModel):
    """微信支付退款基础模式"""

    out_refund_no: str = Field(..., max_length=32, description="商户退款单号")
    refund_fee: int = Field(..., gt=0, description="退款金额，单位为分")
    total_fee: int = Field(..., gt=0, description="原订单金额，单位为分")
    reason: Optional[str] = Field(None, max_length=256, description="退款原因")


class WeChatRefundCreate(WeChatRefundBase):
    """微信支付退款创建模式"""

    out_trade_no: str = Field(..., max_length=32, description="原商户订单号")


class WeChatRefundResponse(BaseModel):
    """微信支付退款响应模式"""

    model_config = ConfigDict(from_attributes=True)

    refund_id: int
    wechat_order_id: int
    out_refund_no: str
    refund_id_wx: Optional[str]
    refund_fee: int
    total_fee: int
    reason: Optional[str]
    refund_status: Optional[str]
    created_at: datetime
    success_time: Optional[datetime]


class WeChatRefundUpdate(BaseModel):
    """微信支付退款更新模式"""

    refund_status: Optional[str] = None
    refund_id_wx: Optional[str] = None
    success_time: Optional[datetime] = None


# 微信支付创建订单响应（包含支付链接）
class WeChatPaymentResponse(BaseModel):
    """微信支付创建订单响应"""

    order_id: int
    out_trade_no: str
    prepay_id: Optional[str]
    code_url: Optional[str]  # 扫码支付链接
    h5_url: Optional[str]  # H5支付链接
    trade_type: WeChatTradeType
    total: int
    description: str
    created_at: datetime


# JSAPI支付参数
class WeChatJSAPIParams(BaseModel):
    """微信JSAPI支付参数"""

    appId: str
    timeStamp: str
    nonceStr: str
    package: str
    signType: str
    paySign: str


# 微信支付配置信息
class WeChatPaymentConfig(BaseModel):
    """微信支付配置信息"""

    api_version: str
    app_id: str
    mch_id: str
    is_configured: bool
    supported_trade_types: list[dict]


# 微信支付统计信息
class WeChatPaymentStats(BaseModel):
    """微信支付统计信息"""

    total_orders: int
    success_orders: int
    pending_orders: int
    failed_orders: int
    total_amount: float  # 总金额（元）
    success_amount: float  # 成功金额（元）
    success_rate: float  # 成功率
    today_orders: int
    today_amount: float
