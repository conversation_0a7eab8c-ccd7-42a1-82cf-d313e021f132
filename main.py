# FastAPI 软件授权系统
# 基于 FastAPI 框架的企业级软件授权管理系统
#
# 此文件是应用的主入口点，整合了完整的业务功能：
# - 用户管理和认证
# - 产品和密钥管理
# - 支付系统
# - 管理员系统
# - 审计日志
#
# 项目结构遵循 FastAPI 最佳实践和标准化规范

import logging
import os
import traceback

# 启用 tracemalloc 来追踪内存分配和协程泄漏
import tracemalloc
import warnings
from contextlib import asynccontextmanager

tracemalloc.start()

# 配置警告过滤器，显示协程相关警告
warnings.filterwarnings("default", category=RuntimeWarning, module="asyncio")

from fastapi import FastAPI, HTTPException, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from starlette.exceptions import HTTPException as StarletteHTTPException

from app.api.versions import create_api_router, create_legacy_router

# 导入应用核心模块
from app.core.config import settings
from app.db.database import close_db

# 导入所有模型以确保表结构被正确注册到 SQLAlchemy metadata
from app.schemas.common import HealthResponse


# 配置日志系统
def get_log_level(debug_level: int) -> int:
    """根据 DEBUG_LEVEL 返回对应的日志级别"""
    if debug_level == 0:
        return logging.WARNING  # 生产模式 - 只显示警告和错误
    elif debug_level == 1:
        return logging.INFO  # 基础调试 - 显示信息、警告、错误
    elif debug_level == 2:
        return logging.DEBUG  # 详细调试 - 显示所有日志
    else:  # debug_level >= 3
        return logging.DEBUG  # 完整调试 - 显示所有日志


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""

    # 颜色代码
    COLORS = {
        "DEBUG": "\033[94m",  # 蓝色
        "INFO": "\033[92m",  # 绿色
        "WARNING": "\033[93m",  # 黄色
        "ERROR": "\033[91m",  # 红色
        "CRITICAL": "\033[95m",  # 紫色
        "RESET": "\033[0m",  # 重置颜色
    }

    def format(self, record):
        # 获取原始格式化的消息
        log_message = super().format(record)

        # 根据日志级别添加颜色
        level_name = record.levelname
        if level_name in self.COLORS:
            colored_level = (
                f"{self.COLORS[level_name]}{level_name}{self.COLORS['RESET']}"
            )
            # 替换级别名称为彩色版本
            log_message = log_message.replace(level_name, colored_level, 1)

        return log_message


def setup_colored_logging(debug_level: int):
    """设置彩色日志配置"""
    log_level = get_log_level(debug_level)

    # 创建根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)

    # 清除现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)

    # 根据调试级别设置不同的格式
    if debug_level == 0:
        # 生产模式 - 简洁格式
        formatter = ColoredFormatter("%(levelname)s: %(message)s")
    elif debug_level == 1:
        # 基础调试 - 包含时间
        formatter = ColoredFormatter("%(asctime)s - %(levelname)s: %(message)s")
    else:
        # 详细调试 - 包含模块信息
        formatter = ColoredFormatter(
            "%(asctime)s - %(name)s - %(levelname)s: %(message)s"
        )

    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)


# 设置彩色日志
setup_colored_logging(settings.DEBUG_LEVEL)

# 获取当前日志级别用于其他模块配置
log_level = get_log_level(settings.DEBUG_LEVEL)

# 设置各模块日志级别
logging.getLogger("uvicorn").setLevel(log_level)
if settings.DEBUG_LEVEL >= 2:
    logging.getLogger("uvicorn.access").setLevel(logging.INFO)
else:
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)

# 控制 WatchFiles 日志级别 - 只在完整调试模式下显示
if settings.DEBUG_LEVEL >= 3:
    logging.getLogger("watchfiles").setLevel(logging.DEBUG)
else:
    logging.getLogger("watchfiles").setLevel(logging.WARNING)

logging.getLogger("app.database").setLevel(logging.WARNING)
logger = logging.getLogger(__name__)


# 应用生命周期管理
@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用启动和关闭时的生命周期管理"""
    logger.info(
        "Application startup complete - Database initialization required manually"
    )
    yield
    # 关闭时清理资源
    await close_db()


# 创建 FastAPI 应用实例
app = FastAPI(
    title=settings.APP_NAME,
    description="FastAPI-based software licensing system",
    version=settings.APP_VERSION,
    docs_url="/docs" if settings.SHOW_DOCS_INFO else None,
    redoc_url="/redoc" if settings.SHOW_DOCS_INFO else None,
    openapi_url="/api/openapi.json" if settings.SHOW_DOCS_INFO else None,
    lifespan=lifespan,
    swagger_ui_parameters={
        "defaultModelsExpandDepth": -1,
        "deepLinking": True,
        "displayRequestDuration": True,
        "filter": True,
        "syntaxHighlight.theme": "monokai",
        "docExpansion": "list",
        "showExtensions": True,
        "persistAuthorization": True,
    }
    if settings.SHOW_DOCS_INFO
    else None,
    swagger_ui_oauth2_redirect_url="/docs/oauth2-redirect"
    if settings.SHOW_DOCS_INFO
    else None,
)

# 挂载静态文件
try:
    app.mount("/static", StaticFiles(directory="static"), name="static")
except RuntimeError:
    # 如果 static 目录不存在，创建一个空目录或跳过
    logger.warning("Static directory not found, skipping static file mounting")


# Favicon 路由
@app.get("/favicon.ico", include_in_schema=False)
async def favicon():
    """返回 favicon.ico"""
    from pathlib import Path

    favicon_files = ["chip-icon.ico", "favicon.ico"]
    static_dir = Path("static")

    for filename in favicon_files:
        favicon_path = static_dir / filename
        if favicon_path.exists():
            return FileResponse(favicon_path, media_type="image/x-icon")

    # 如果没有找到 favicon 文件，返回 404
    raise HTTPException(status_code=404, detail="Favicon not found")


# 配置 CORS 中间件
cors_origins = os.getenv("CORS_ORIGINS", "*").split(",")
cors_credentials = os.getenv("CORS_CREDENTIALS", "true").lower() == "true"
cors_methods = os.getenv("CORS_METHODS", "GET,POST,PUT,DELETE,OPTIONS").split(",")
cors_headers = os.getenv("CORS_HEADERS", "*").split(",")

logger.info(f"CORS Origins: {cors_origins}")
logger.info(f"CORS Credentials: {cors_credentials}")
logger.info(f"CORS Methods: {cors_methods}")
logger.info(f"CORS Headers: {cors_headers}")

app.add_middleware(
    CORSMiddleware,
    allow_origins=cors_origins,
    allow_credentials=cors_credentials,
    allow_methods=cors_methods,
    allow_headers=cors_headers,
    max_age=int(os.getenv("CORS_MAX_AGE", "3600")),
)


# 自定义异常处理
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """处理请求验证错误"""
    errors = []
    for error in exc.errors():
        error_msg = {
            "loc": error.get("loc", []),
            "msg": error.get("msg", ""),
            "type": error.get("type", ""),
        }
        errors.append(error_msg)

    logger.error(f"Request validation error: {errors}")

    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, content={"detail": errors}
    )


@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """处理HTTP异常"""
    logger.error(f"HTTP exception: {exc.status_code} - {exc.detail}")

    return JSONResponse(status_code=exc.status_code, content={"detail": exc.detail})


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """处理所有其他异常 - 根据 DEBUG_LEVEL 显示不同详细程度"""
    logger.error(f"Unhandled exception: {str(exc)}")
    if settings.DEBUG_LEVEL >= 2:
        logger.error(traceback.format_exc())

    # Return different levels of error details based on DEBUG_LEVEL
    if settings.DEBUG_LEVEL == 0:
        # Production mode: basic error info only
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"detail": "Internal server error"},
        )
    elif settings.DEBUG_LEVEL == 1:
        # Basic debug: show error type
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "detail": "Internal server error, please contact administrator",
                "error_type": type(exc).__name__,
            },
        )
    elif settings.DEBUG_LEVEL == 2:
        # Detailed debug: include request info
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "detail": "Internal server error, please contact administrator",
                "debug_info": {
                    "error_message": str(exc),
                    "error_type": type(exc).__name__,
                    "request_url": str(request.url),
                    "request_method": request.method,
                },
            },
        )
    else:  # DEBUG_LEVEL >= 3
        # Full debug: include stack trace
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "detail": "Internal server error, please contact administrator",
                "debug_info": {
                    "error_message": str(exc),
                    "error_type": type(exc).__name__,
                    "traceback": traceback.format_exc().split("\n"),
                    "request_url": str(request.url),
                    "request_method": request.method,
                    "request_headers": dict(request.headers),
                    "debug_level": settings.DEBUG_LEVEL,
                },
            },
        )


# 根路径
@app.get("/")
async def root():
    """根路径，返回API基本信息"""
    # 基础认证信息
    auth_info = {
        "access_mode": settings.USER_ACCESS_MODE,
        "access_mode_number": settings.get_access_mode_number(),
        "access_mode_description": settings.get_access_mode_description(),
        "user_registration_allowed": settings.allow_user_registration,
        "user_login_allowed": settings.allow_user_login,
        "admin_login": "/api/admin/login",
        "admin_token": "/api/admin/token",
    }

    # 根据访问模式添加相应的端点
    if settings.allow_user_registration:
        auth_info["user_registration"] = "/api/auth/register"

    if settings.allow_user_login:
        auth_info["user_login"] = "/api/auth/login"

    # 向后兼容的字段
    auth_info.update(
        {
            "admin_only_mode": settings.ADMIN_ONLY_MODE,  # 向后兼容
        }
    )

    response_data = {
        "message": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "api_base_url": settings.API_BASE_STR,
        "authentication": auth_info,
        "notice": settings.get_access_mode_description(),
    }

    # 只有在启用文档显示时才包含文档链接
    if settings.SHOW_DOCS_INFO:
        response_data.update(
            {
                "docs": "/docs",
                "redoc": "/redoc",
            }
        )

    return response_data


# 健康检查端点
@app.get("/health", response_model=HealthResponse)
async def health_check_get(db_query: bool = True):
    """健康检查端点 - GET方法"""
    return await _health_check_logic(db_query)


@app.head("/health")
async def health_check_head(db_query: bool = True):
    """健康检查端点 - HEAD方法（UptimeRobot使用）"""
    # HEAD请求默认执行数据库查询，用于保活
    result = await _health_check_logic(db_query=db_query)
    return result


async def _health_check_logic(db_query: bool = True):
    """健康检查端点"""
    # 检查数据库连接
    db_connected = False
    supabase_connected = False

    user_count = None

    try:
        from app.db.database import get_database_info

        db_info = get_database_info()
        db_connected = db_info["engine_available"]
        supabase_connected = db_info["supabase_configured"]

        # 如果请求执行数据库查询（用于Supabase保活）
        if db_query and db_connected:
            try:
                from app.db.database import health_db_keepalive

                # 使用专用健康检查引擎执行查询
                user_count = await health_db_keepalive()

                if user_count is not None:
                    logger.info(f"🔄 数据库保活查询成功: {user_count} 个用户")
                else:
                    logger.info("🔄 数据库保活查询跳过（专用引擎不可用）")

            except Exception as e:
                logger.warning(f"数据库保活查询失败: {e}")

        # Log database type info
        logger.info(
            f"Health check - Database type: {db_info['database_type']}, Connection status: {db_connected}, Keepalive: {db_query}"
        )

    except Exception as e:
        logger.warning(
            f"Database connection check failed during health check: {str(e)}"
        )

    return HealthResponse(
        status="healthy",
        message="API is running successfully",
        version=settings.APP_VERSION,
        database_connected=db_connected,
        supabase_connected=supabase_connected,
    )


# 注册API路由
# 主API路由
api_router = create_api_router()
app.include_router(api_router)

# 向后兼容的旧版本路由（不在API文档中显示）
legacy_router = create_legacy_router()
app.include_router(legacy_router)


# 应用入口点
# Vercel部署时不需要启动配置，应用实例会被自动导入和运行
if __name__ == "__main__":
    import uvicorn

    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True, log_level="info")
