name: Keep Vercel App Alive

on:
  schedule:
    # 每6小时运行一次 (UTC时间)
    - cron: '0 */6 * * *'
  workflow_dispatch: # 允许手动触发

jobs:
  keepalive:
    runs-on: ubuntu-latest

    steps:
    - name: Ping Vercel App
      run: |
        echo "🚀 Pinging Vercel app to keep it alive..."

        # 从环境变量获取应用URL，如果没有则使用默认值
        APP_URL="${{ secrets.VERCEL_APP_URL || 'https://your-app.vercel.app' }}"

        # Ping健康检查端点（执行数据库查询）
        response=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL/health?db_query=true" || echo "000")

        if [ "$response" = "200" ]; then
          echo "✅ App is alive! Response: $response"
        else
          echo "⚠️ App response: $response"
          # 尝试ping根路径
          root_response=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL/" || echo "000")
          echo "🔄 Root path response: $root_response"
        fi

        echo "🎉 Keepalive ping completed"

    - name: Ping Supabase (if configured)
      run: |
        echo "🗄️ Checking Supabase connection..."

        # 如果配置了Supabase URL，也ping一下
        SUPABASE_URL="${{ secrets.SUPABASE_URL }}"

        if [ ! -z "$SUPABASE_URL" ]; then
          supabase_response=$(curl -s -o /dev/null -w "%{http_code}" "$SUPABASE_URL/rest/v1/"             -H "apikey: ${{ secrets.SUPABASE_ANON_KEY }}" || echo "000")
          echo "📊 Supabase response: $supabase_response"
        else
          echo "ℹ️ Supabase URL not configured"
        fi
