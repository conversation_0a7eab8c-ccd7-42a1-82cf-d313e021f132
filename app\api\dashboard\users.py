"""
Dashboard 用户管理API
提供增强的用户管理功能，仅限超级管理员访问
"""

import logging
import secrets
import string
from datetime import datetime, timedelta
from typing import Optional, Union

from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi import status
from sqlalchemy import or_

from app.core.admin import require_super_admin, get_admin_info
from app.core.security import get_password_hash
from app.models.user import User, UserRole, UserStatus
from .schemas import (
    BatchOperationResult,
    BatchStatusUpdateRequest,
    DashboardUserResponse,
    DashboardUsersResponse,
    FilterInfo,
    PaginationInfo,
    PasswordUpdateRequest,
    SuccessResponse,
    UserCreateRequest,
    UserCreateResponse,
    UserStatsResponse,
    UserUpdateRequest,
    UserDetailedResponse,
    UserKeyInfo,
    UserPaymentInfo,
    UserWeChatOrderInfo,
    UserLogInfo,
    UserStatsInfo,
)

logger = logging.getLogger(__name__)
router = APIRouter()


def generate_temporary_password(length: int = 12) -> str:
    """生成临时密码"""
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(length))


def build_user_response(user: User) -> DashboardUserResponse:
    """构建用户响应对象"""
    return DashboardUserResponse(
        user_id=user.user_id,
        email=user.email,
        nickname=user.nickname,
        status=user.status.value,
        role=user.role.value,
        register_date=user.register_date.isoformat() if user.register_date else None,
        last_login_date=user.last_login_date.isoformat() if user.last_login_date else None,
        email_verified=user.email_verified,
        remarks=getattr(user, 'remarks', None)
    )


@router.get("", response_model=DashboardUsersResponse, operation_id="dashboard_get_users")
async def get_users_enhanced(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(10, ge=1, le=100, description="返回的记录数"),
    search: Optional[str] = Query(None, description="搜索关键词（邮箱、昵称）"),
    status: Optional[UserStatus] = Query(None, description="用户状态过滤"),
    role: Optional[UserRole] = Query(None, description="用户角色过滤"),
    date_from: Optional[str] = Query(None, description="注册日期起始时间 YYYY-MM-DD"),
    date_to: Optional[str] = Query(None, description="注册日期结束时间 YYYY-MM-DD"),
    sort_by: str = Query("register_date", description="排序字段"),
    sort_order: str = Query("desc", description="排序方向 asc/desc"),
    current_admin: Union[User, dict] = Depends(require_super_admin()),
):
    """
    获取用户列表（增强版）
    支持搜索、过滤、排序等功能
    仅限超级管理员访问
    """
    try:
        # 构建查询条件
        conditions = []
        
        # 搜索条件
        if search:
            search_conditions = [User.email.ilike(f"%{search}%")]
            # 只有当nickname不为None时才添加nickname搜索条件
            search_conditions.append(User.nickname.ilike(f"%{search}%"))
            search_condition = or_(*search_conditions)
            conditions.append(search_condition)
        
        # 状态过滤
        if status:
            conditions.append(User.status == status)
        
        # 角色过滤
        if role:
            conditions.append(User.role == role)
        
        # 日期范围过滤
        if date_from:
            try:
                from_date = datetime.strptime(date_from, "%Y-%m-%d")
                conditions.append(User.register_date >= from_date)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid date_from format. Use YYYY-MM-DD"
                )
        
        if date_to:
            try:
                to_date = datetime.strptime(date_to, "%Y-%m-%d") + timedelta(days=1)
                conditions.append(User.register_date < to_date)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid date_to format. Use YYYY-MM-DD"
                )
        
        # 使用隔离连接避免"another operation is in progress"错误
        from app.db.database import execute_isolated_query

        # 构建WHERE条件字符串
        where_conditions = []
        query_params = []
        param_index = 1

        # 搜索条件
        if search:
            where_conditions.append(f"(email ILIKE ${param_index} OR nickname ILIKE ${param_index})")
            query_params.append(f"%{search}%")
            param_index += 1

        # 状态过滤
        if status:
            where_conditions.append(f"status = ${param_index}")
            query_params.append(status.value)
            param_index += 1

        # 角色过滤
        if role:
            where_conditions.append(f"role = ${param_index}")
            query_params.append(role.value)
            param_index += 1

        # 日期范围过滤
        if date_from:
            try:
                from_date = datetime.strptime(date_from, "%Y-%m-%d")
                where_conditions.append(f"register_date >= ${param_index}")
                query_params.append(from_date)
                param_index += 1
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid date_from format. Use YYYY-MM-DD"
                )

        if date_to:
            try:
                to_date = datetime.strptime(date_to, "%Y-%m-%d") + timedelta(days=1)
                where_conditions.append(f"register_date < ${param_index}")
                query_params.append(to_date)
                param_index += 1
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid date_to format. Use YYYY-MM-DD"
                )

        # 构建WHERE子句
        where_clause = ""
        if where_conditions:
            where_clause = "WHERE " + " AND ".join(where_conditions)

        # 获取总数
        count_query = f"SELECT COUNT(*) FROM users {where_clause}"
        count_result = await execute_isolated_query(count_query, tuple(query_params))
        total_count = count_result[0]["count"]

        # 构建排序
        valid_sort_fields = {
            'user_id': 'user_id',
            'email': 'email',
            'nickname': 'nickname',
            'status': 'status',
            'role': 'role',
            'register_date': 'register_date',
            'last_login_date': 'last_login_date',
            'email_verified': 'email_verified'
        }

        sort_field = valid_sort_fields.get(sort_by, 'register_date')
        sort_direction = "DESC" if sort_order.lower() == "desc" else "ASC"

        # 构建主查询
        main_query = f"""
        SELECT user_id, email, password_hash, status, role, register_date,
               last_login_date, nickname, email_verified, email_verification_code,
               email_verification_expires, remarks
        FROM users
        {where_clause}
        ORDER BY {sort_field} {sort_direction}
        LIMIT ${param_index} OFFSET ${param_index + 1}
        """

        query_params.extend([limit, skip])
        rows = await execute_isolated_query(main_query, tuple(query_params))

        # 构建响应
        dashboard_users = []
        for row in rows:
            user_data = DashboardUserResponse(
                user_id=row["user_id"],
                email=row["email"],
                nickname=row["nickname"],
                status=row["status"],
                role=row["role"],
                register_date=row["register_date"].isoformat() if row["register_date"] else None,
                last_login_date=row["last_login_date"].isoformat() if row["last_login_date"] else None,
                email_verified=row["email_verified"],
                remarks=row["remarks"]
            )
            dashboard_users.append(user_data)
        
        # 计算分页信息
        page = (skip // limit) + 1
        total_pages = (total_count + limit - 1) // limit
        
        pagination = PaginationInfo(
            total=total_count,
            page=page,
            size=limit,
            pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )
        
        # 过滤信息
        filters_applied = FilterInfo(
            search=search,
            status=status.value if status else None,
            role=role.value if role else None,
            date_from=date_from,
            date_to=date_to,
            total_filtered=total_count
        )
        
        return DashboardUsersResponse(
            users=dashboard_users,
            pagination=pagination,
            filters_applied=filters_applied
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get enhanced users list: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get users list"
        )


@router.post("", response_model=UserCreateResponse, operation_id="dashboard_create_user")
async def create_user(
    user_data: UserCreateRequest,
    current_admin: Union[User, dict] = Depends(require_super_admin()),
):
    """
    创建新用户
    自动生成临时密码，仅限超级管理员访问
    """
    try:
        # 使用隔离查询避免并发冲突
        from app.db.database import execute_isolated_query
        import asyncpg
        from app.core.config import settings

        # 检查邮箱是否已存在
        check_query = "SELECT user_id FROM users WHERE email = $1"
        existing_result = await execute_isolated_query(check_query, (user_data.email,))

        if existing_result:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already exists"
            )

        # 生成临时密码
        temp_password = generate_temporary_password()
        hashed_password = get_password_hash(temp_password)

        # 获取数据库URL并创建独立连接进行插入操作
        database_url = settings.get_effective_database_url()
        if database_url.startswith("postgresql+asyncpg://"):
            asyncpg_url = database_url.replace("postgresql+asyncpg://", "postgresql://")
        elif database_url.startswith("postgres+asyncpg://"):
            asyncpg_url = database_url.replace("postgres+asyncpg://", "postgresql://")
        else:
            asyncpg_url = database_url

        conn = await asyncpg.connect(asyncpg_url, statement_cache_size=0, command_timeout=30)

        try:
            # 插入新用户
            insert_query = """
            INSERT INTO users (email, password_hash, nickname, role, status, email_verified, remarks, register_date)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            RETURNING user_id, email, nickname, status, role, register_date, last_login_date, email_verified, remarks
            """

            result = await conn.fetchrow(
                insert_query,
                user_data.email,
                hashed_password,
                user_data.nickname,
                user_data.role.value,
                user_data.status.value,
                user_data.email_verified,
                user_data.remarks,
                datetime.now()
            )

            logger.info(f"Super admin created new user: {user_data.email}")

            # 构建响应
            user_response = DashboardUserResponse(
                user_id=result["user_id"],
                email=result["email"],
                nickname=result["nickname"],
                status=result["status"],
                role=result["role"],
                register_date=result["register_date"].isoformat() if result["register_date"] else None,
                last_login_date=result["last_login_date"].isoformat() if result["last_login_date"] else None,
                email_verified=result["email_verified"],
                remarks=result["remarks"]
            )

            return UserCreateResponse(
                success=True,
                message="User created successfully",
                user=user_response,
                temporary_password=temp_password
            )

        finally:
            await conn.close()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create user"
        )


@router.get("/stats", response_model=UserStatsResponse, operation_id="dashboard_user_stats")
async def get_user_statistics(
    current_admin: Union[User, dict] = Depends(require_super_admin()),
):
    """
    获取用户统计数据
    仅限超级管理员访问
    """
    try:
        # 使用隔离连接避免"another operation is in progress"错误
        from app.db.database import execute_isolated_query

        # 构建统计查询 - 一次性获取所有统计数据
        seven_days_ago = datetime.now() - timedelta(days=7)

        # 首先检查基本的用户统计
        basic_stats_query = """
        SELECT
            COUNT(*) as total_users,
            COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as active_users,
            COUNT(CASE WHEN status = 'BANNED' THEN 1 END) as banned_users,
            COUNT(CASE WHEN status = 'PENDING' THEN 1 END) as pending_users,
            COUNT(CASE WHEN email_verified = true THEN 1 END) as verified_users,
            COUNT(CASE WHEN email_verified = false THEN 1 END) as unverified_users,
            COUNT(CASE WHEN role = 'USER' THEN 1 END) as regular_users,
            COUNT(CASE WHEN role = 'ADMIN' THEN 1 END) as admin_users,
            COUNT(CASE WHEN role = 'SUPER_ADMIN' THEN 1 END) as super_admin_users,
            COUNT(CASE WHEN register_date >= $1 THEN 1 END) as recent_registrations
        FROM users
        """

        result = await execute_isolated_query(basic_stats_query, (seven_days_ago,))
        stats = result[0]

        total_users = stats["total_users"]
        active_users = stats["active_users"]
        banned_users = stats["banned_users"]
        pending_users = stats["pending_users"]
        verified_users = stats["verified_users"]
        unverified_users = stats["unverified_users"]
        regular_users = stats["regular_users"]
        admin_users = stats["admin_users"]
        super_admin_users = stats["super_admin_users"]
        recent_registrations = stats["recent_registrations"]

        return UserStatsResponse(
            total_users=total_users or 0,
            active_users=active_users or 0,
            banned_users=banned_users or 0,
            pending_users=pending_users or 0,
            verified_users=verified_users or 0,
            unverified_users=unverified_users or 0,
            users_by_role={
                "USER": regular_users or 0,
                "ADMIN": admin_users or 0,
                "SUPER_ADMIN": super_admin_users or 0
            },
            recent_registrations={
                "this_week": recent_registrations or 0
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get user statistics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user statistics"
        )


@router.get("/{user_id}", response_model=DashboardUserResponse, operation_id="dashboard_get_user")
async def get_user_by_id(
    user_id: int,
    current_admin: Union[User, dict] = Depends(require_super_admin()),
):
    """
    获取单个用户详情
    仅限超级管理员访问
    """
    try:
        # 使用隔离查询避免并发冲突
        from app.db.database import execute_isolated_query

        query = """
        SELECT user_id, email, password_hash, status, role, register_date,
               last_login_date, nickname, email_verified, email_verification_code,
               email_verification_expires, remarks
        FROM users
        WHERE user_id = $1
        """

        result = await execute_isolated_query(query, (user_id,))

        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        user_data = result[0]
        return DashboardUserResponse(
            user_id=user_data["user_id"],
            email=user_data["email"],
            nickname=user_data["nickname"],
            status=user_data["status"],
            role=user_data["role"],
            register_date=user_data["register_date"].isoformat() if user_data["register_date"] else None,
            last_login_date=user_data["last_login_date"].isoformat() if user_data["last_login_date"] else None,
            email_verified=user_data["email_verified"],
            remarks=user_data["remarks"]
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user"
        )


@router.put("/{user_id}", response_model=DashboardUserResponse, operation_id="dashboard_update_user")
async def update_user(
    user_id: int,
    user_data: UserUpdateRequest,
    current_admin: Union[User, dict] = Depends(require_super_admin()),
):
    """
    完整更新用户信息
    仅限超级管理员访问
    """
    try:
        # 使用隔离查询避免并发冲突
        from app.db.database import execute_isolated_query
        import asyncpg
        from app.core.config import settings

        # 首先检查用户是否存在
        check_query = """
        SELECT user_id, email, nickname, status, role, register_date,
               last_login_date, email_verified, remarks
        FROM users WHERE user_id = $1
        """
        user_result = await execute_isolated_query(check_query, (user_id,))

        if not user_result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        current_user = user_result[0]

        # 检查邮箱是否已被其他用户使用
        if user_data.email and user_data.email != current_user["email"]:
            email_check_query = "SELECT user_id FROM users WHERE email = $1 AND user_id != $2"
            existing_result = await execute_isolated_query(email_check_query, (user_data.email, user_id))
            if existing_result:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email already exists"
                )

        # 准备更新数据
        update_data = user_data.model_dump(exclude_unset=True)
        if not update_data:
            # 如果没有要更新的数据，直接返回当前用户信息
            return DashboardUserResponse(
                user_id=current_user["user_id"],
                email=current_user["email"],
                nickname=current_user["nickname"],
                status=current_user["status"],
                role=current_user["role"],
                register_date=current_user["register_date"].isoformat() if current_user["register_date"] else None,
                last_login_date=current_user["last_login_date"].isoformat() if current_user["last_login_date"] else None,
                email_verified=current_user["email_verified"],
                remarks=current_user["remarks"]
            )

        # 构建更新查询
        set_clauses = []
        params = []
        param_index = 1

        for field, value in update_data.items():
            if field == "role":
                set_clauses.append(f"{field} = ${param_index}")
                params.append(value.value)
            elif field == "status":
                set_clauses.append(f"{field} = ${param_index}")
                params.append(value.value)
            else:
                set_clauses.append(f"{field} = ${param_index}")
                params.append(value)
            param_index += 1

        params.append(user_id)  # 添加WHERE条件的参数

        update_query = f"""
        UPDATE users
        SET {', '.join(set_clauses)}
        WHERE user_id = ${param_index}
        RETURNING user_id, email, nickname, status, role, register_date,
                  last_login_date, email_verified, remarks
        """

        # 获取数据库URL并创建独立连接进行更新操作
        database_url = settings.get_effective_database_url()
        if database_url.startswith("postgresql+asyncpg://"):
            asyncpg_url = database_url.replace("postgresql+asyncpg://", "postgresql://")
        elif database_url.startswith("postgres+asyncpg://"):
            asyncpg_url = database_url.replace("postgres+asyncpg://", "postgresql://")
        else:
            asyncpg_url = database_url

        conn = await asyncpg.connect(asyncpg_url, statement_cache_size=0, command_timeout=30)

        try:
            result = await conn.fetchrow(update_query, *params)

            logger.info(f"Super admin updated user {user_id}: {result['email']}")

            return DashboardUserResponse(
                user_id=result["user_id"],
                email=result["email"],
                nickname=result["nickname"],
                status=result["status"],
                role=result["role"],
                register_date=result["register_date"].isoformat() if result["register_date"] else None,
                last_login_date=result["last_login_date"].isoformat() if result["last_login_date"] else None,
                email_verified=result["email_verified"],
                remarks=result["remarks"]
            )

        finally:
            await conn.close()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user"
        )


@router.put("/{user_id}/password", response_model=SuccessResponse, operation_id="dashboard_update_password")
async def update_user_password(
    user_id: int,
    password_data: PasswordUpdateRequest,
    current_admin: Union[User, dict] = Depends(require_super_admin()),
):
    """
    修改用户密码
    仅限超级管理员访问
    """
    try:
        # 使用隔离查询避免并发冲突
        from app.db.database import execute_isolated_query
        import asyncpg
        from app.core.config import settings

        # 首先检查用户是否存在
        check_query = "SELECT user_id, email FROM users WHERE user_id = $1"
        user_result = await execute_isolated_query(check_query, (user_id,))

        if not user_result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        user_data = user_result[0]

        # 生成新密码哈希
        new_password_hash = get_password_hash(password_data.new_password)

        # 获取数据库URL并创建独立连接进行更新操作
        database_url = settings.get_effective_database_url()
        if database_url.startswith("postgresql+asyncpg://"):
            asyncpg_url = database_url.replace("postgresql+asyncpg://", "postgresql://")
        elif database_url.startswith("postgres+asyncpg://"):
            asyncpg_url = database_url.replace("postgres+asyncpg://", "postgresql://")
        else:
            asyncpg_url = database_url

        conn = await asyncpg.connect(asyncpg_url, statement_cache_size=0, command_timeout=30)

        try:
            # 更新密码
            update_query = "UPDATE users SET password_hash = $1 WHERE user_id = $2"
            await conn.execute(update_query, new_password_hash, user_id)

            logger.info(f"Super admin updated password for user {user_id}: {user_data['email']}")

            return SuccessResponse(
                success=True,
                message="Password updated successfully"
            )

        finally:
            await conn.close()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update password for user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update password"
        )


@router.post("/batch", response_model=BatchOperationResult, operation_id="dashboard_batch_update_status")
async def batch_update_user_status(
    request: BatchStatusUpdateRequest,
    current_admin: Union[User, dict] = Depends(require_super_admin()),
):
    """
    批量更新用户状态
    仅限超级管理员访问
    """
    try:
        # 使用隔离查询避免并发冲突
        from app.db.database import execute_isolated_query
        import asyncpg
        from app.core.config import settings

        success_count = 0
        failed_count = 0
        failed_users = []

        # 获取数据库URL并创建独立连接进行批量更新操作
        database_url = settings.get_effective_database_url()
        if database_url.startswith("postgresql+asyncpg://"):
            asyncpg_url = database_url.replace("postgresql+asyncpg://", "postgresql://")
        elif database_url.startswith("postgres+asyncpg://"):
            asyncpg_url = database_url.replace("postgres+asyncpg://", "postgresql://")
        else:
            asyncpg_url = database_url

        conn = await asyncpg.connect(asyncpg_url, statement_cache_size=0, command_timeout=30)

        try:
            for user_id in request.user_ids:
                try:
                    # 检查用户是否存在
                    check_query = "SELECT user_id FROM users WHERE user_id = $1"
                    user_exists = await conn.fetchval(check_query, user_id)

                    if not user_exists:
                        failed_users.append({
                            "user_id": user_id,
                            "error": "User not found"
                        })
                        failed_count += 1
                        continue

                    # 更新用户状态
                    update_query = "UPDATE users SET status = $1 WHERE user_id = $2"
                    await conn.execute(update_query, request.new_status.value, user_id)
                    success_count += 1

                except Exception as e:
                    failed_users.append({
                        "user_id": user_id,
                        "error": str(e)
                    })
                    failed_count += 1

            logger.info(f"Super admin batch updated {success_count} users status to {request.new_status.value}")

            return BatchOperationResult(
                success=failed_count == 0,
                message=f"Batch operation completed: {success_count} successful, {failed_count} failed",
                results={
                    "success_count": success_count,
                    "failed_count": failed_count,
                    "failed_users": failed_users
                }
            )

        finally:
            await conn.close()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Batch operation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Batch operation failed"
        )


@router.delete("/{user_id}", response_model=SuccessResponse, operation_id="dashboard_delete_user")
async def delete_user(
    user_id: int,
    current_admin: Union[User, dict] = Depends(require_super_admin()),
):
    """
    删除用户
    仅限超级管理员访问
    注意：这是硬删除操作，会永久删除用户及其相关数据
    """
    try:
        # 使用隔离查询避免并发冲突
        from app.db.database import execute_isolated_query
        import asyncpg
        from app.core.config import settings

        # 获取用户信息
        user_query = """
        SELECT user_id, email, role
        FROM users WHERE user_id = $1
        """
        user_result = await execute_isolated_query(user_query, (user_id,))

        if not user_result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        user_data = user_result[0]

        # 防止删除超级管理员
        if user_data["role"] == "SUPER_ADMIN":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot delete super admin user"
            )

        # 防止删除当前登录的管理员
        current_admin_info = get_admin_info(current_admin)
        if user_data["email"] == current_admin_info.get('email'):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot delete yourself"
            )

        # 记录删除操作信息
        user_email = user_data["email"]
        user_role = user_data["role"]

        # 获取数据库URL并创建独立连接进行删除操作
        database_url = settings.get_effective_database_url()
        if database_url.startswith("postgresql+asyncpg://"):
            asyncpg_url = database_url.replace("postgresql+asyncpg://", "postgresql://")
        elif database_url.startswith("postgres+asyncpg://"):
            asyncpg_url = database_url.replace("postgres+asyncpg://", "postgresql://")
        else:
            asyncpg_url = database_url

        conn = await asyncpg.connect(asyncpg_url, statement_cache_size=0, command_timeout=30)

        try:
            # 删除用户（硬删除）
            delete_query = "DELETE FROM users WHERE user_id = $1"
            result = await conn.execute(delete_query, user_id)

            # 检查是否真的删除了用户
            if result == "DELETE 0":
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found or already deleted"
                )

            logger.warning(f"Super admin {current_admin_info['email']} deleted user: {user_email} (role: {user_role})")

            return SuccessResponse(
                success=True,
                message=f"User {user_email} has been permanently deleted"
            )

        finally:
            await conn.close()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete user"
        )





@router.get("/{user_id}/detailed", response_model=UserDetailedResponse, operation_id="dashboard_get_user_detailed")
async def get_user_detailed(
    user_id: int,
    include_keys: bool = Query(False, description="是否包含密钥信息"),
    include_payments: bool = Query(False, description="是否包含支付记录"),
    include_orders: bool = Query(False, description="是否包含微信订单"),
    include_logs: bool = Query(False, description="是否包含操作日志"),
    limit_records: int = Query(10, ge=1, le=100, description="关联记录限制数量"),
    current_admin: Union[User, dict] = Depends(require_super_admin()),
):
    """
    获取用户详细信息（包含关联数据）
    仅限超级管理员访问
    """
    try:
        # 使用隔离查询获取用户基本信息
        from app.db.database import execute_isolated_query

        user_query = """
        SELECT user_id, email, nickname, status, role, register_date,
               last_login_date, email_verified, remarks
        FROM users WHERE user_id = $1
        """
        user_result = await execute_isolated_query(user_query, (user_id,))

        if not user_result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        user_data = user_result[0]

        # 使用隔离查询获取统计信息，添加表存在性检查
        try:
            stats_query = """
            SELECT
                (SELECT COUNT(*) FROM keys WHERE user_id = $1) as total_keys,
                (SELECT COUNT(*) FROM keys WHERE user_id = $1 AND status = 'ACTIVE') as active_keys,
                (SELECT COUNT(*) FROM keys WHERE user_id = $1 AND status = 'EXPIRED') as expired_keys,
                (SELECT COALESCE(SUM(amount), 0) FROM payment_records WHERE user_id = $1) as total_payments,
                (SELECT COUNT(*) FROM payment_records WHERE user_id = $1 AND payment_type = 'PURCHASE') as successful_payments,
                (SELECT COUNT(*) FROM wechat_orders WHERE user_id = $1) as total_orders,
                (SELECT COUNT(*) FROM wechat_orders WHERE user_id = $1 AND trade_state = 'SUCCESS') as successful_orders,
                (SELECT COUNT(*) FROM user_logs WHERE user_id = $1) as total_logs,
                (SELECT MAX(action_date) FROM user_logs WHERE user_id = $1) as last_activity
            """

            stats_result = await execute_isolated_query(stats_query, (user_id,))
        except Exception as e:
            logger.warning(f"Failed to get detailed stats for user {user_id}, using basic stats: {e}")
            # 如果详细统计失败，使用基本统计
            stats_result = [{"total_keys": 0, "active_keys": 0, "expired_keys": 0,
                           "total_payments": 0.0, "successful_payments": 0, "total_orders": 0,
                           "successful_orders": 0, "total_logs": 0, "last_activity": None}]
        stats_data = stats_result[0] if stats_result else {}

        # 构建统计信息
        stats = UserStatsInfo(
            total_keys=stats_data.get("total_keys", 0) or 0,
            active_keys=stats_data.get("active_keys", 0) or 0,
            expired_keys=stats_data.get("expired_keys", 0) or 0,
            total_payments=float(stats_data.get("total_payments", 0) or 0),
            successful_payments=stats_data.get("successful_payments", 0) or 0,
            total_orders=stats_data.get("total_orders", 0) or 0,
            successful_orders=stats_data.get("successful_orders", 0) or 0,
            total_logs=stats_data.get("total_logs", 0) or 0,
            last_activity_date=stats_data.get("last_activity").isoformat() if stats_data.get("last_activity") else None
        )

        # 构建基本响应
        response = UserDetailedResponse(
            user_id=user_data["user_id"],
            email=user_data["email"],
            nickname=user_data["nickname"],
            status=user_data["status"],
            role=user_data["role"],
            register_date=user_data["register_date"].isoformat() if user_data["register_date"] else None,
            last_login_date=user_data["last_login_date"].isoformat() if user_data["last_login_date"] else None,
            email_verified=user_data["email_verified"],
            remarks=user_data["remarks"],
            stats=stats
        )

        # 根据需要加载关联数据 - 使用隔离查询
        is_admin = user_data["role"] in ["ADMIN", "SUPER_ADMIN"]

        if include_keys:
            try:
                keys_query = """
                SELECT key_id, key_string, product_id, price, start_date, end_date,
                       activated_count, max_activation_count, status, created_date
                FROM keys
                WHERE user_id = $1
                ORDER BY created_date DESC
                LIMIT $2
                """
                keys_result = await execute_isolated_query(keys_query, (user_id, limit_records))

                if keys_result:
                    response.keys = [
                        UserKeyInfo(
                            key_id=key["key_id"],
                            key_string=key["key_string"],
                            product_id=key["product_id"],
                            price=float(key["price"]),
                            start_date=key["start_date"].isoformat(),
                            end_date=key["end_date"].isoformat(),
                            activated_count=key["activated_count"],
                            max_activation_count=key["max_activation_count"],
                            status=key["status"],
                            created_date=key["created_date"].isoformat()
                        ) for key in keys_result
                    ]
            except Exception as e:
                logger.warning(f"Failed to get keys for user {user_id}: {e}")
                response.keys = []

        if include_payments:
            try:
                payments_query = """
                SELECT payment_id, amount, payment_type, payment_date, product_id, order_id, payment_method
                FROM payment_records
                WHERE user_id = $1
                ORDER BY payment_date DESC
                LIMIT $2
                """
                payments_result = await execute_isolated_query(payments_query, (user_id, limit_records))

                if payments_result:
                    response.payments = [
                        UserPaymentInfo(
                            payment_id=payment["payment_id"],
                            amount=float(payment["amount"]),
                            payment_type=payment["payment_type"],
                            payment_date=payment["payment_date"].isoformat(),
                            product_id=payment["product_id"],
                            order_id=payment["order_id"],
                            payment_method=payment["payment_method"]
                        ) for payment in payments_result
                    ]
            except Exception as e:
                logger.warning(f"Failed to get payments for user {user_id}: {e}")
                response.payments = []

        if include_orders:
            try:
                orders_query = """
                SELECT order_id, out_trade_no, transaction_id, description, total,
                       trade_type, trade_state, created_at, success_time, notify_received
                FROM wechat_orders
                WHERE user_id = $1
                ORDER BY created_at DESC
                LIMIT $2
                """
                orders_result = await execute_isolated_query(orders_query, (user_id, limit_records))

                if orders_result:
                    response.wechat_orders = [
                        UserWeChatOrderInfo(
                            order_id=order["order_id"],
                            out_trade_no=order["out_trade_no"],
                            transaction_id=order["transaction_id"],
                            description=order["description"],
                            total=order["total"],
                            trade_type=order["trade_type"],
                            trade_state=order["trade_state"],
                            created_at=order["created_at"].isoformat(),
                            success_time=order["success_time"].isoformat() if order["success_time"] else None,
                            notify_received=bool(order["notify_received"])
                        ) for order in orders_result
                    ]
            except Exception as e:
                logger.warning(f"Failed to get orders for user {user_id}: {e}")
                response.wechat_orders = []

        if include_logs:
            try:
                logs_query = """
                SELECT log_id, action, action_date, details
                FROM user_logs
                WHERE user_id = $1
                ORDER BY action_date DESC
                LIMIT $2
                """
                logs_result = await execute_isolated_query(logs_query, (user_id, limit_records))

                if logs_result:
                    response.logs = [
                        UserLogInfo(
                            log_id=log["log_id"],
                            action=log["action"],
                            action_date=log["action_date"].isoformat(),
                            details=log["details"]
                        ) for log in logs_result
                    ]
            except Exception as e:
                logger.warning(f"Failed to get logs for user {user_id}: {e}")
                response.logs = []

        logger.info(f"Retrieved detailed info for user {user_id} with {len(response.keys or [])} keys, {len(response.payments or [])} payments, {len(response.wechat_orders or [])} orders, {len(response.logs or [])} logs")

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get detailed user info for {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get detailed user information"
        )
