"""
用户相关Pydantic模式定义
"""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict, Field, field_validator

from app.schemas.common import UserRole, UserStatus


# 用户相关模式
class UserBase(BaseModel):
    """用户基础模式"""

    email: str  # 改为str类型，支持邮箱或手机号
    nickname: Optional[str] = Field(None, max_length=50)

    @field_validator("email")
    @classmethod
    def validate_email_or_phone(cls, v: str) -> str:
        """验证邮箱或手机号格式"""
        import re

        # 检查是否是邮箱格式
        email_pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        if re.match(email_pattern, v):
            return v

        # 检查是否是手机号格式（中国手机号）
        phone_pattern = r"^1[3-9]\d{9}$"
        if re.match(phone_pattern, v):
            return v

        # 如果都不匹配，抛出验证错误
        raise ValueError("Must be a valid email address or Chinese mobile phone number")


class UserCreate(UserBase):
    """用户创建模式"""

    password: str = Field(..., min_length=8, max_length=100)


class UserUpdate(BaseModel):
    """用户更新模式"""

    email: Optional[str] = None  # 改为str类型，支持邮箱或手机号
    nickname: Optional[str] = Field(None, max_length=50)
    status: Optional[UserStatus] = None
    email_verified: Optional[bool] = None
    remarks: Optional[str] = None

    @field_validator("email")
    @classmethod
    def validate_email_or_phone(cls, v: Optional[str]) -> Optional[str]:
        """验证邮箱或手机号格式"""
        if v is None:
            return v

        import re

        # 检查是否是邮箱格式
        email_pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        if re.match(email_pattern, v):
            return v

        # 检查是否是手机号格式（中国手机号）
        phone_pattern = r"^1[3-9]\d{9}$"
        if re.match(phone_pattern, v):
            return v

        # 如果都不匹配，抛出验证错误
        raise ValueError("Must be a valid email address or Chinese mobile phone number")


class UserResponse(UserBase):
    """用户响应模式"""

    model_config = ConfigDict(from_attributes=True)

    user_id: int
    status: UserStatus
    role: UserRole
    register_date: datetime
    last_login_date: Optional[datetime] = None
    email_verified: bool
    remarks: Optional[str] = None


class UserLogin(BaseModel):
    """用户登录模式 - 支持邮箱或手机号"""

    email: str  # 改为str类型，支持邮箱或手机号
    password: str

    @field_validator("email")
    @classmethod
    def validate_email_or_phone(cls, v: str) -> str:
        """验证邮箱或手机号格式"""
        import re

        # 检查是否是邮箱格式
        email_pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        if re.match(email_pattern, v):
            return v

        # 检查是否是手机号格式（中国手机号）
        phone_pattern = r"^1[3-9]\d{9}$"
        if re.match(phone_pattern, v):
            return v

        # 如果都不匹配，抛出验证错误
        raise ValueError("Must be a valid email address or Chinese mobile phone number")

    @field_validator("password")
    @classmethod
    def validate_password(cls, v: str) -> str:
        """验证密码不为空"""
        if not v or not v.strip():
            raise ValueError("Password cannot be empty")
        return v
