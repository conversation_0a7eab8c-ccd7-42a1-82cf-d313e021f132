"""
数据库配置和连接管理
支持多种数据库：PostgreSQL, MySQL, SQLite
可选支持 Supabase
"""

import logging
from typing import AsyncGenerator

from dotenv import load_dotenv
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import DeclarativeBase

# 可选的 Supabase 支持
try:
    from supabase import Client, create_client

    SUPABASE_AVAILABLE = True
except ImportError:
    SUPABASE_AVAILABLE = False
    Client = None
    create_client = None

# 加载环境变量
load_dotenv()

# 配置日志
logger = logging.getLogger(__name__)

# 数据库配置 - 支持 Vercel 自动配置
from app.core.config import settings

DATABASE_URL = settings.get_effective_database_url()
SUPABASE_URL = settings.SUPABASE_URL
SUPABASE_ANON_KEY = settings.SUPABASE_ANON_KEY
SUPABASE_SERVICE_ROLE_KEY = settings.SUPABASE_SERVICE_ROLE_KEY


def detect_database_type(database_url: str) -> str:
    """检测数据库类型"""
    if not database_url:
        return "none"

    database_url_lower = database_url.lower()
    if "postgresql" in database_url_lower or "postgres" in database_url_lower:
        return "postgresql"
    elif "mysql" in database_url_lower:
        return "mysql"
    elif "sqlite" in database_url_lower:
        return "sqlite"
    else:
        return "unknown"


def convert_to_async_url(database_url: str) -> str:
    """将数据库 URL 转换为异步版本，并彻底移除 SSL 参数"""
    if not database_url:
        return database_url

    db_type = detect_database_type(database_url)

    # 彻底移除所有 SSL 相关参数的简单方法
    def remove_ssl_params(url: str) -> str:
        """彻底移除所有 SSL 参数"""
        # 分离基础 URL 和查询参数
        if "?" in url:
            base_url, query_string = url.split("?", 1)
        else:
            return url

        # 移除所有 SSL 相关参数和其他可能导致问题的参数
        problematic_params = [
            "sslmode",
            "sslcert",
            "sslkey",
            "sslrootcert",
            "sslcrl",
            "sslcompression",
            "ssl",
            "ssl_ca",
            "ssl_cert",
            "ssl_key",
            "supa",  # Supabase 特有参数，asyncpg 不支持
        ]

        # 分割查询参数
        params = []
        for param in query_string.split("&"):
            if "=" in param:
                key = param.split("=")[0]
                if key not in problematic_params:
                    params.append(param)
            elif param not in problematic_params:
                params.append(param)

        # 重建 URL
        if params:
            return f"{base_url}?{'&'.join(params)}"
        else:
            return base_url

    # 首先移除 SSL 参数
    clean_url = remove_ssl_params(database_url)

    # 然后转换为异步驱动
    if db_type == "postgresql":
        # PostgreSQL: postgresql:// -> postgresql+asyncpg://
        if "postgresql+asyncpg://" not in clean_url:
            clean_url = clean_url.replace(
                "postgresql://", "postgresql+asyncpg://"
            ).replace("postgres://", "postgresql+asyncpg://")
    elif db_type == "mysql":
        # MySQL: mysql:// -> mysql+aiomysql://
        if "mysql+aiomysql://" not in clean_url:
            clean_url = clean_url.replace("mysql://", "mysql+aiomysql://")
    elif db_type == "sqlite":
        # SQLite: sqlite:// -> sqlite+aiosqlite://
        if "sqlite+aiosqlite://" not in clean_url:
            clean_url = clean_url.replace("sqlite://", "sqlite+aiosqlite://")

    logger.info(f"URL 转换: {database_url[:30]}... -> {clean_url[:30]}...")
    return clean_url


def get_engine_config(db_type: str) -> dict:
    """根据数据库类型获取引擎配置（优化无服务器环境）"""
    import os

    # 检测是否在Vercel环境
    is_vercel = os.getenv("VERCEL") == "1"
    database_url = os.getenv("DATABASE_URL", "") or os.getenv("POSTGRES_URL", "")
    is_supabase = "supabase" in database_url.lower() or "pooler" in database_url.lower()

    # 详细的环境检测日志
    logger.info(f"🔍 环境检测: VERCEL={os.getenv('VERCEL')}, is_vercel={is_vercel}")
    logger.info(f"🔍 数据库检测: database_url包含supabase={('supabase' in database_url.lower())}, 包含pooler={('pooler' in database_url.lower())}, is_supabase={is_supabase}")
    logger.info(f"🔍 将使用配置分支: {'Vercel+Supabase' if (is_vercel or is_supabase) else '传统PostgreSQL'}")

    # 无服务器环境的基础配置
    base_config = {
        "echo": False,
        "pool_pre_ping": False,  # 在无服务器环境中禁用
        "pool_recycle": -1,  # 禁用连接回收
    }

    if db_type == "postgresql":
        if is_vercel or is_supabase:
            # Vercel + Supabase 无服务器优化配置
            config = {
                **base_config,
                "pool_size": 1,  # 无服务器环境使用单连接
                "max_overflow": 0,  # 不允许额外连接，避免复杂性
                "pool_timeout": 30,  # 短超时，快速失败
                "pool_reset_on_return": None,  # 禁用重置
                "pool_pre_ping": False,  # 禁用ping，减少操作
                "pool_recycle": -1,  # 不回收连接
                "connect_args": {
                    "statement_cache_size": 0,  # 禁用prepared statements
                    "prepared_statement_cache_size": 0,  # 额外保险
                    "command_timeout": 30,  # 短命令超时
                    "server_settings": {
                        "application_name": "FastAuth_Serverless",
                        "jit": "off",  # 禁用JIT编译
                    },
                },
                # SQLAlchemy层面禁用prepared statements
                "execution_options": {
                    "compiled_cache": {},  # 禁用编译缓存
                    "autocommit": False,
                },
            }
            logger.info(f"✅ 使用 Vercel + Supabase 优化配置 (pool_size={config['pool_size']}, max_overflow={config['max_overflow']}, 总计{config['pool_size'] + config['max_overflow']}个连接)")
        else:
            # 传统PostgreSQL无限制配置
            config = {
                **base_config,
                "pool_size": 15,  # 大幅增加连接数
                "max_overflow": 25,  # 允许大量额外连接
                "pool_timeout": 60,  # 大幅增加超时时间
                "pool_recycle": 3600,  # 增加连接回收时间
                "pool_pre_ping": True,
                "pool_reset_on_return": "commit",
            }
            logger.info(f"✅ 使用传统 PostgreSQL 配置 (pool_size={config['pool_size']}, max_overflow={config['max_overflow']}, 总计{config['pool_size'] + config['max_overflow']}个连接)")

        return config
    elif db_type == "mysql":
        return {
            **base_config,
            "pool_size": 10,  # 大幅增加MySQL连接数
            "max_overflow": 15,  # 允许大量额外连接
            "pool_recycle": 3600,
            "pool_timeout": 30,  # 增加超时时间
        }
    elif db_type == "sqlite":
        return {
            **base_config,
            "pool_size": 5,  # 即使SQLite也增加连接数
            "max_overflow": 5,  # 允许额外连接
            "pool_timeout": 30,  # 增加超时时间
        }
    else:
        return base_config


# SQLAlchemy配置
class Base(DeclarativeBase):
    """数据库模型基类"""

    pass


# 创建异步数据库引擎
engine = None
async_session_maker = None
health_check_engine = None  # 健康检查专用引擎
database_type = "none"

if DATABASE_URL:
    try:
        # 检测数据库类型
        database_type = detect_database_type(DATABASE_URL)
        logger.info(f"检测到数据库类型: {database_type}")

        # 转换为异步 URL
        async_database_url = convert_to_async_url(DATABASE_URL)
        logger.info(f"使用异步数据库 URL: {async_database_url[:50]}...")

        # 获取引擎配置
        engine_config = get_engine_config(database_type)

        # 创建引擎
        engine = create_async_engine(async_database_url, **engine_config)

        # 创建会话工厂
        async_session_maker = async_sessionmaker(
            engine, class_=AsyncSession, expire_on_commit=False
        )

        logger.info(f"数据库引擎创建成功: {database_type}")

        # 创建健康检查专用引擎
        try:
            health_check_config = {
                "echo": False,
                "pool_size": 10,  # 极大增加连接数，确保健康检查永不阻塞
                "max_overflow": 15,  # 允许极大量额外连接
                "pool_timeout": 30,  # 大幅增加超时时间
                "pool_recycle": -1,  # 不回收连接
                "connect_args": {
                    "statement_cache_size": 0,  # 禁用prepared statements
                    "prepared_statement_cache_size": 0,  # 额外保险
                    "command_timeout": 30,  # 大幅增加命令超时
                },
            }

            health_check_engine = create_async_engine(
                async_database_url, **health_check_config
            )
            logger.info("健康检查专用引擎创建成功")
        except Exception as he:
            logger.warning(f"健康检查引擎创建失败: {he}")
            health_check_engine = None

    except Exception as e:
        logger.error(f"数据库引擎创建失败: {e}")
        logger.info("尝试使用简化的数据库连接...")

        # 备用方案：使用简化的连接配置
        try:
            # 简单的 URL 转换，移除所有查询参数
            simple_url = DATABASE_URL.split("?")[0]  # 移除查询参数
            if database_type == "postgresql":
                if "postgresql+asyncpg://" not in simple_url:
                    simple_url = simple_url.replace(
                        "postgresql://", "postgresql+asyncpg://"
                    ).replace("postgres://", "postgresql+asyncpg://")

            # 使用无限制配置，彻底禁用prepared statements
            simple_config = {
                "echo": False,
                "pool_pre_ping": True,
                "pool_size": 8,  # 大幅增加连接数支持高并发
                "max_overflow": 12,  # 允许大量额外连接
                "pool_timeout": 30,  # 大幅增加超时时间
                "connect_args": {
                    "statement_cache_size": 0,  # 禁用prepared statements
                    "prepared_statement_cache_size": 0,  # 额外保险
                    "server_settings": {
                        "jit": "off",  # 禁用JIT编译
                    },
                },
                # SQLAlchemy层面禁用prepared statements
                "execution_options": {
                    "compiled_cache": {},  # 禁用编译缓存
                    "autocommit": False,
                },
            }

            engine = create_async_engine(simple_url, **simple_config)
            async_session_maker = async_sessionmaker(
                engine, class_=AsyncSession, expire_on_commit=False
            )

            logger.info(f"⚠️ 使用备用简化配置创建数据库引擎成功 (pool_size={simple_config['pool_size']}, max_overflow={simple_config['max_overflow']})")

            # 在备用方案中也创建健康检查引擎
            try:
                health_check_config = {
                    "echo": False,
                    "pool_size": 5,  # 大幅增加连接数
                    "max_overflow": 5,  # 允许大量额外连接
                    "pool_timeout": 15,  # 大幅增加超时
                    "pool_recycle": -1,
                    "connect_args": {
                        "statement_cache_size": 0,  # 禁用prepared statements
                        "prepared_statement_cache_size": 0,  # 额外保险
                        "command_timeout": 15,  # 大幅增加命令超时
                    },
                }

                health_check_engine = create_async_engine(
                    simple_url, **health_check_config
                )
                logger.info(
                    "备用方案：健康检查引擎创建成功（已禁用prepared statements）"
                )
            except Exception as he2:
                logger.warning(f"备用方案：健康检查引擎创建失败: {he2}")
                health_check_engine = None

        except Exception as e2:
            logger.error(f"简化配置也失败: {e2}")
            engine = None
            async_session_maker = None
            health_check_engine = None

# Supabase客户端（可选）
supabase = None
supabase_admin = None

if SUPABASE_AVAILABLE and create_client and SUPABASE_URL and SUPABASE_ANON_KEY:
    try:
        supabase = create_client(SUPABASE_URL, SUPABASE_ANON_KEY)
        logger.info("Supabase 客户端初始化成功")
    except Exception as e:
        logger.warning(f"Supabase 客户端初始化失败: {e}")

if SUPABASE_AVAILABLE and create_client and SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY:
    try:
        supabase_admin = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)
        logger.info("Supabase 管理客户端初始化成功")
    except Exception as e:
        logger.warning(f"Supabase 管理客户端初始化失败: {e}")


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """获取数据库会话（优化无服务器环境）"""
    if not async_session_maker:
        raise RuntimeError(
            "Database not configured. Please set DATABASE_URL environment variable."
        )

    session = None
    try:
        session = async_session_maker()
        yield session
    except Exception:
        if session:
            try:
                await session.rollback()
            except Exception as rollback_error:
                logger.warning(f"Session rollback failed: {rollback_error}")
        raise
    finally:
        if session:
            try:
                import asyncio
                import os

                # 在Vercel环境中使用超时关闭
                is_vercel = os.getenv("VERCEL") == "1"
                if is_vercel:
                    await asyncio.wait_for(session.close(), timeout=1.0)
                else:
                    await session.close()
            except Exception as close_error:
                logger.debug(f"Session close warning (可忽略): {close_error}")


def get_supabase():
    """获取Supabase客户端"""
    if not SUPABASE_AVAILABLE:
        raise RuntimeError(
            "Supabase not available. Please install supabase package: pip install supabase"
        )
    if not supabase:
        raise RuntimeError(
            "Supabase not configured. Please set SUPABASE_URL and SUPABASE_ANON_KEY environment variables."
        )
    return supabase


def get_supabase_admin():
    """获取Supabase管理客户端"""
    if not SUPABASE_AVAILABLE:
        raise RuntimeError(
            "Supabase not available. Please install supabase package: pip install supabase"
        )
    if not supabase_admin:
        raise RuntimeError(
            "Supabase admin not configured. Please set SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables."
        )
    return supabase_admin


def get_database_info() -> dict:
    """获取数据库信息"""
    return {
        "database_type": database_type,
        "database_url_configured": bool(DATABASE_URL),
        "engine_available": bool(engine),
        "supabase_available": SUPABASE_AVAILABLE,
        "supabase_configured": bool(supabase),
        "supabase_admin_configured": bool(supabase_admin),
    }


async def init_db():
    """初始化数据库表 - 使用原生SQL完全避免并发冲突"""
    if not engine:
        logger.warning("数据库引擎未配置，跳过表创建")
        return

    # 创建专用的初始化引擎，完全隔离
    init_engine = None
    try:
        logger.info("🚀 开始数据库初始化（使用原生SQL方案）...")

        # 获取数据库URL
        from app.core.config import settings
        database_url = settings.get_effective_database_url()

        if not database_url:
            raise Exception("无法获取数据库URL")

        # 转换为异步URL
        async_database_url = convert_to_async_url(database_url)

        # 创建专用初始化引擎 - 完全独立的连接池
        init_config = {
            "echo": False,
            "pool_size": 1,  # 单连接，避免复杂性
            "max_overflow": 0,  # 不允许额外连接
            "pool_timeout": 60,  # 长超时
            "pool_recycle": -1,  # 不回收连接
            "connect_args": {
                "statement_cache_size": 0,  # 禁用prepared statements
                "prepared_statement_cache_size": 0,  # 额外保险
                "command_timeout": 60,  # 长命令超时
            },
        }

        init_engine = create_async_engine(async_database_url, **init_config)
        logger.info("✅ 专用初始化引擎创建成功")

        # 使用原生SQL创建表，完全避开SQLAlchemy的检查机制
        await _create_tables_with_native_sql(init_engine)

    except Exception as e:
        logger.error(f"❌ 数据库表创建失败: {e}")
        raise
    finally:
        # 确保专用引擎被正确关闭
        if init_engine:
            try:
                await init_engine.dispose()
                logger.info("🔌 专用初始化引擎已关闭")
            except Exception as e:
                logger.warning(f"关闭专用初始化引擎时出现警告: {e}")


async def _create_tables_with_native_sql(init_engine):
    """使用纯asyncpg创建表，完全避免SQLAlchemy和prepared statement冲突"""
    import asyncpg
    from app.core.config import settings

    # 获取数据库URL并转换为asyncpg格式
    database_url = settings.get_effective_database_url()
    if database_url.startswith('postgresql+asyncpg://'):
        database_url = database_url.replace('postgresql+asyncpg://', 'postgresql://')
    elif database_url.startswith('postgres://'):
        database_url = database_url.replace('postgres://', 'postgresql://')

    # 创建直接的asyncpg连接，禁用prepared statements
    conn = await asyncpg.connect(database_url, statement_cache_size=0)

    try:
        logger.info("🔧 开始使用纯asyncpg创建数据库表...")

        # 定义枚举类型创建命令
        enum_commands = [
            """DO $$ BEGIN
                CREATE TYPE userstatus AS ENUM ('PENDING', 'ACTIVE', 'SUSPENDED', 'DELETED');
            EXCEPTION
                WHEN duplicate_object THEN null;
            END $$""",
            """DO $$ BEGIN
                CREATE TYPE userrole AS ENUM ('USER', 'ADMIN', 'SUPER_ADMIN');
            EXCEPTION
                WHEN duplicate_object THEN null;
            END $$""",
            """DO $$ BEGIN
                CREATE TYPE keytype AS ENUM ('TRIAL', 'STANDARD', 'PREMIUM', 'ENTERPRISE');
            EXCEPTION
                WHEN duplicate_object THEN null;
            END $$""",
            """DO $$ BEGIN
                CREATE TYPE keystatus AS ENUM ('ACTIVE', 'EXPIRED', 'SUSPENDED', 'REVOKED');
            EXCEPTION
                WHEN duplicate_object THEN null;
            END $$""",
            """DO $$ BEGIN
                CREATE TYPE activationstatus AS ENUM ('PENDING', 'ACTIVE', 'EXPIRED', 'REVOKED');
            EXCEPTION
                WHEN duplicate_object THEN null;
            END $$""",
            """DO $$ BEGIN
                CREATE TYPE paymentstatus AS ENUM ('PENDING', 'COMPLETED', 'FAILED', 'REFUNDED');
            EXCEPTION
                WHEN duplicate_object THEN null;
            END $$""",
            """DO $$ BEGIN
                CREATE TYPE logtype AS ENUM ('LOGIN', 'LOGOUT', 'REGISTER', 'PASSWORD_CHANGE', 'EMAIL_VERIFY', 'KEY_ACTIVATE', 'PAYMENT', 'ADMIN_ACTION', 'SYSTEM');
            EXCEPTION
                WHEN duplicate_object THEN null;
            END $$""",
            """DO $$ BEGIN
                CREATE TYPE wechattradetype AS ENUM ('JSAPI', 'NATIVE', 'APP', 'MWEB');
            EXCEPTION
                WHEN duplicate_object THEN null;
            END $$""",
            """DO $$ BEGIN
                CREATE TYPE wechattradestate AS ENUM ('SUCCESS', 'REFUND', 'NOTPAY', 'CLOSED', 'REVOKED', 'USERPAYING', 'PAYERROR');
            EXCEPTION
                WHEN duplicate_object THEN null;
            END $$"""
        ]

        # 执行枚举类型创建
        for i, cmd in enumerate(enum_commands, 1):
            try:
                await conn.execute(cmd)
                logger.info(f"✅ 枚举类型 {i}/{len(enum_commands)} 创建成功")
            except Exception as e:
                logger.warning(f"枚举类型 {i} 创建警告: {e}")

        # 定义表创建命令
        table_commands = [
            """CREATE TABLE IF NOT EXISTS users (
                user_id SERIAL PRIMARY KEY,
                email VARCHAR(255) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                status userstatus DEFAULT 'PENDING',
                role userrole DEFAULT 'USER',
                register_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login_date TIMESTAMP,
                nickname VARCHAR(50),
                email_verified BOOLEAN DEFAULT FALSE,
                email_verification_code VARCHAR(6),
                email_verification_expires TIMESTAMP,
                remarks TEXT
            )""",
            """CREATE TABLE IF NOT EXISTS products (
                product_id SERIAL PRIMARY KEY,
                product_name VARCHAR(100) NOT NULL,
                description TEXT,
                default_price DECIMAL(10,2),
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )""",
            """CREATE TABLE IF NOT EXISTS keys (
                key_id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(user_id),
                product_id INTEGER REFERENCES products(product_id),
                key_value VARCHAR(255) UNIQUE NOT NULL,
                key_type keytype DEFAULT 'STANDARD',
                status keystatus DEFAULT 'ACTIVE',
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expire_date TIMESTAMP,
                last_used_date TIMESTAMP,
                usage_count INTEGER DEFAULT 0,
                max_usage_count INTEGER,
                remarks TEXT
            )""",
            """CREATE TABLE IF NOT EXISTS key_activations (
                activation_id SERIAL PRIMARY KEY,
                key_id INTEGER REFERENCES keys(key_id),
                user_id INTEGER REFERENCES users(user_id),
                activation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status activationstatus DEFAULT 'PENDING',
                machine_id VARCHAR(255),
                ip_address INET,
                remarks TEXT
            )""",
            """CREATE TABLE IF NOT EXISTS payment_records (
                payment_id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(user_id),
                product_id INTEGER REFERENCES products(product_id),
                amount DECIMAL(10,2) NOT NULL,
                status paymentstatus DEFAULT 'PENDING',
                payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                transaction_id VARCHAR(255),
                order_id VARCHAR(50),
                payment_method VARCHAR(50)
            )""",
            """CREATE TABLE IF NOT EXISTS user_logs (
                log_id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(user_id),
                log_type logtype NOT NULL,
                log_message TEXT,
                ip_address INET,
                user_agent TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )""",
            """CREATE TABLE IF NOT EXISTS wechat_orders (
                order_id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(user_id) NOT NULL,
                product_id INTEGER REFERENCES products(product_id),
                payment_record_id INTEGER REFERENCES payment_records(payment_id),
                out_trade_no VARCHAR(32) UNIQUE NOT NULL,
                transaction_id VARCHAR(64),
                prepay_id VARCHAR(64),
                description VARCHAR(128) NOT NULL,
                total INTEGER NOT NULL,
                trade_type wechattradetype NOT NULL,
                trade_state wechattradestate DEFAULT 'NOTPAY',
                code_url TEXT,
                h5_url TEXT,
                openid VARCHAR(128),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                success_time TIMESTAMP,
                notify_received BOOLEAN DEFAULT FALSE,
                notify_time TIMESTAMP
            )""",
            """CREATE TABLE IF NOT EXISTS wechat_refunds (
                refund_id SERIAL PRIMARY KEY,
                wechat_order_id INTEGER REFERENCES wechat_orders(order_id) NOT NULL,
                out_refund_no VARCHAR(32) UNIQUE NOT NULL,
                refund_id_wx VARCHAR(64),
                refund_fee INTEGER NOT NULL,
                total_fee INTEGER NOT NULL,
                reason VARCHAR(256),
                refund_status VARCHAR(32),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                success_time TIMESTAMP
            )"""
        ]

        # 执行表创建
        table_names = ["users", "products", "keys", "key_activations", "payment_records", "user_logs", "wechat_orders", "wechat_refunds"]
        for i, (cmd, name) in enumerate(zip(table_commands, table_names), 1):
            try:
                await conn.execute(cmd)
                logger.info(f"✅ 表 {name} ({i}/{len(table_commands)}) 创建成功")
            except Exception as e:
                logger.warning(f"表 {name} 创建警告: {e}")

        # 定义索引创建命令
        index_commands = [
            "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
            "CREATE INDEX IF NOT EXISTS idx_users_last_login ON users(last_login_date)",
            "CREATE INDEX IF NOT EXISTS idx_keys_user_id ON keys(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_keys_product_id ON keys(product_id)",
            "CREATE INDEX IF NOT EXISTS idx_key_activations_key_id ON key_activations(key_id)",
            "CREATE INDEX IF NOT EXISTS idx_key_activations_user_id ON key_activations(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_payment_records_user_id ON payment_records(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_payment_records_product_id ON payment_records(product_id)",
            "CREATE INDEX IF NOT EXISTS idx_user_logs_user_id ON user_logs(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_wechat_orders_user_id ON wechat_orders(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_wechat_orders_out_trade_no ON wechat_orders(out_trade_no)",
            "CREATE INDEX IF NOT EXISTS idx_wechat_orders_transaction_id ON wechat_orders(transaction_id)",
            "CREATE INDEX IF NOT EXISTS idx_wechat_orders_created_at ON wechat_orders(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_wechat_refunds_wechat_order_id ON wechat_refunds(wechat_order_id)",
            "CREATE INDEX IF NOT EXISTS idx_wechat_refunds_out_refund_no ON wechat_refunds(out_refund_no)"
        ]

        # 执行索引创建
        for i, cmd in enumerate(index_commands, 1):
            try:
                await conn.execute(cmd)
                logger.info(f"✅ 索引 {i}/{len(index_commands)} 创建成功")
            except Exception as e:
                logger.warning(f"索引 {i} 创建警告: {e}")

        logger.info(f"✅ 数据库初始化完成: {', '.join(table_names)}")

    except Exception as e:
        logger.error(f"❌ 纯asyncpg数据库初始化失败: {e}")
        raise
    finally:
        await conn.close()


async def execute_isolated_query(query: str, params: tuple = None):
    """执行隔离查询，每次使用独立连接避免并发冲突"""
    import asyncpg
    from app.core.config import settings

    # 获取数据库URL
    database_url = settings.get_effective_database_url()
    if not database_url:
        raise Exception("数据库URL未配置")

    # 转换为asyncpg格式
    if database_url.startswith("postgresql+asyncpg://"):
        asyncpg_url = database_url.replace("postgresql+asyncpg://", "postgresql://")
    elif database_url.startswith("postgres+asyncpg://"):
        asyncpg_url = database_url.replace("postgres+asyncpg://", "postgresql://")
    else:
        asyncpg_url = database_url

    # 创建独立连接
    conn = await asyncpg.connect(
        asyncpg_url,
        statement_cache_size=0,  # 禁用prepared statements
        command_timeout=30
    )

    try:
        if params:
            result = await conn.fetch(query, *params)
        else:
            result = await conn.fetch(query)
        return result
    finally:
        await conn.close()


async def health_db_keepalive():
    """健康检查专用数据库查询（直接使用asyncpg避免SQLAlchemy冲突）"""
    # 使用全局锁避免并发冲突
    if not hasattr(health_db_keepalive, "_lock"):
        import asyncio

        health_db_keepalive._lock = asyncio.Lock()

    # 如果已有查询在进行，直接返回
    if health_db_keepalive._lock.locked():
        logger.info("健康检查查询跳过（另一个查询正在进行）")
        return None

    try:
        import asyncio

        import asyncpg

        from app.core.config import settings

        async with health_db_keepalive._lock:
            # 直接使用asyncpg连接，完全绕过SQLAlchemy
            database_url = settings.get_effective_database_url()
            if not database_url:
                return None

            # 转换为asyncpg格式的URL
            if database_url.startswith("postgresql+asyncpg://"):
                asyncpg_url = database_url.replace(
                    "postgresql+asyncpg://", "postgresql://"
                )
            elif database_url.startswith("postgres+asyncpg://"):
                asyncpg_url = database_url.replace(
                    "postgres+asyncpg://", "postgresql://"
                )
            else:
                asyncpg_url = database_url

            # 直接使用asyncpg连接，禁用prepared statements
            conn = await asyncio.wait_for(
                asyncpg.connect(asyncpg_url, statement_cache_size=0, command_timeout=2),
                timeout=3.0,
            )

            try:
                # 执行简单查询
                result = await asyncio.wait_for(
                    conn.fetchval("SELECT COUNT(*) FROM users"), timeout=2.0
                )
                return result
            finally:
                await conn.close()

    except asyncio.TimeoutError:
        logger.warning("健康检查数据库查询超时")
        return None
    except Exception as e:
        logger.warning(f"健康检查数据库查询失败: {e}")
        return None


async def close_db():
    """关闭数据库连接（优化无服务器环境）"""
    global health_check_engine

    # 关闭健康检查引擎
    if health_check_engine:
        try:
            await health_check_engine.dispose()
            logger.info("健康检查引擎已关闭")
        except Exception as e:
            logger.warning(f"关闭健康检查引擎时出现警告: {e}")

    # 关闭主引擎
    if engine:
        try:
            import asyncio
            import os

            # 在Vercel环境中使用更温和的关闭方式
            is_vercel = os.getenv("VERCEL") == "1"

            if is_vercel:
                # Vercel环境：快速关闭，避免事件循环问题
                try:
                    # 设置短超时，避免阻塞
                    await asyncio.wait_for(engine.dispose(), timeout=1.0)
                    logger.info("数据库连接已快速关闭（Vercel环境）")
                except asyncio.TimeoutError:
                    logger.info("数据库连接关闭超时，但这在Vercel环境中是正常的")
                except Exception as e:
                    # 在Vercel中忽略关闭错误，因为函数即将终止
                    logger.debug(f"Vercel环境数据库关闭警告（可忽略）: {e}")
            else:
                # 本地环境：正常关闭
                await engine.dispose()
                logger.info("数据库连接已正确关闭")

        except Exception as e:
            logger.warning(f"关闭数据库连接时出现警告: {e}")
