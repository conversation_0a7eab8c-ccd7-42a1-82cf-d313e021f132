"""
原网站API客户端服务和Legacy Integration接口
用于调用原网站API验证老用户，实现无缝登录迁移
基于原网站接口.txt文档实现
"""

import logging
from datetime import datetime
from typing import Any, Dict, Optional

import httpx
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.security import create_access_token, get_password_hash
from app.db.database import get_db
from app.models.user import User, UserRole, UserStatus
from app.schemas.common import AuthResponse, Token
from app.schemas.user import UserResponse

logger = logging.getLogger(__name__)

# ========================================
# 原网站API配置
# ========================================

LEGACY_API_BASE_URL = "https://www.00123.com/api/user"

# ========================================
# 原网站API响应数据模型
# ========================================


class LegacyApiResponse(BaseModel):
    """原网站API统一响应格式"""

    msg: str
    code: int
    data: Any


class LegacyUserData(BaseModel):
    """原网站用户数据格式"""

    id: int  # 改为int类型，因为原网站返回的是整数
    accountNo: str
    zone: Optional[str] = None
    password: str
    userEmail: Optional[str] = None
    userNickName: str
    userHeadImg: Optional[str] = None
    userSignature: Optional[str] = None
    showId: str
    createDate: str
    updateDate: Optional[str] = None
    lastLoginDate: Optional[str] = None
    source: str
    status: int
    blackStatus: int
    authorityStatus: Optional[int] = None
    remarks: Optional[str] = None
    createBy: Optional[str] = None
    updateBy: Optional[str] = None
    searchContent: Optional[str] = None
    wxOpenid: Optional[str] = None
    qqOpenid: Optional[str] = None
    wbId: Optional[str] = None
    userType: Optional[str] = None
    delFlag: Optional[bool] = None  # 添加缺失的字段


# ========================================
# 原网站API客户端类
# ========================================


class LegacyWebsiteClient:
    """原网站API客户端，用于调用原网站接口验证用户"""

    def __init__(self, base_url: str = LEGACY_API_BASE_URL):
        self.base_url = base_url
        self.timeout = 30.0

    async def check_account_exists(self, account: str) -> bool:
        """
        检查账号是否在原网站已注册
        调用接口2: POST https://www.00123.com/api/user/check/register
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout, verify=False) as client:
                url = f"{self.base_url}/check/register"
                request_data = {"account": account}
                response = await client.post(url, json=request_data)

                if response.status_code == 200:
                    result = response.json()
                    # data为false表示已注册，true表示未注册
                    return not result.get("data", True)
                else:
                    logger.error(
                        f"检查账号失败: {response.status_code} - {response.text}"
                    )
                    return False

        except Exception as e:
            logger.error(f"调用原网站API检查账号失败: {str(e)}")
            return False

    async def verify_user_login(
        self, account: str, password: str
    ) -> Optional[LegacyUserData]:
        """
        验证用户登录并获取用户信息
        调用接口4: POST https://www.00123.com/api/user/login
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout, verify=False) as client:
                url = f"{self.base_url}/login"
                login_data = {
                    "account": account,
                    "password": password,
                    "remember": 1,  # 根据接口文档，使用1而不是False
                    "type": 0,  # 账号密码登录
                }

                response = await client.post(url, json=login_data)

                if response.status_code == 200:
                    result = response.json()
                    if result.get("code") == 200 and result.get("data"):
                        # 解析用户数据
                        user_data = result["data"]
                        return LegacyUserData(**user_data)
                    else:
                        logger.warning(
                            f"原网站登录失败: {result.get('msg', '未知错误')}"
                        )
                        return None
                else:
                    logger.error(
                        f"原网站登录请求失败: {response.status_code} - {response.text}"
                    )
                    return None

        except Exception as e:
            logger.error(f"调用原网站API登录失败: {str(e)}")
            return None


# ========================================
# 数据转换工具函数
# ========================================


def convert_legacy_user_to_local_user(legacy_user: LegacyUserData) -> Dict[str, Any]:
    """将原网站用户数据转换为本地用户数据格式"""
    # 解析时间格式，转换为无时区的datetime（数据库要求）
    try:
        create_date_with_tz = datetime.fromisoformat(
            legacy_user.createDate.replace("+0800", "+08:00")
        )
        create_date = create_date_with_tz.replace(tzinfo=None)  # 移除时区信息
    except (ValueError, AttributeError):
        create_date = datetime.now()

    last_login_date = None
    if legacy_user.lastLoginDate:
        try:
            last_login_date_with_tz = datetime.fromisoformat(
                legacy_user.lastLoginDate.replace("+0800", "+08:00")
            )
            last_login_date = last_login_date_with_tz.replace(
                tzinfo=None
            )  # 移除时区信息
        except (ValueError, AttributeError):
            last_login_date = datetime.now()

    # 确定邮箱地址
    # 如果原网站有邮箱，使用邮箱；否则直接使用账号（手机号）
    email = legacy_user.userEmail or legacy_user.accountNo

    return {
        "email": email,
        "password_hash": get_password_hash(
            "legacy_migrated_user"
        ),  # 临时密码，用户需要重置
        "status": UserStatus.ACTIVE if legacy_user.status == 0 else UserStatus.PENDING,
        "role": UserRole.USER,
        "register_date": create_date,
        "last_login_date": last_login_date,
        "email_verified": True,  # 从原网站迁移的用户默认已验证
        # 存储原网站的额外信息
        "legacy_user_id": str(legacy_user.id),  # 转换为字符串
        "legacy_account_no": legacy_user.accountNo,
        "legacy_nickname": legacy_user.userNickName,
        "legacy_show_id": legacy_user.showId,
    }


# ========================================
# 全局客户端实例
# ========================================

legacy_client = LegacyWebsiteClient()

# ========================================
# 混合登录服务函数
# ========================================


async def try_legacy_login(
    account: str, password: str, db: AsyncSession
) -> Optional[User]:
    """
    尝试通过原网站验证用户登录，如果成功则在本地创建用户

    Args:
        account: 用户账号（手机号或邮箱）
        password: 密码
        db: 数据库会话

    Returns:
        User: 成功时返回本地用户对象，失败时返回None
    """
    try:
        # 1. 调用原网站API验证用户
        legacy_user = await legacy_client.verify_user_login(account, password)

        if not legacy_user:
            logger.info(f"原网站验证失败: {account}")
            return None

        logger.info(f"原网站验证成功: {account}, 用户ID: {legacy_user.id}")

        # 2. 检查本地是否已存在该用户
        # 直接使用手机号或邮箱，不转换格式
        email = legacy_user.userEmail or legacy_user.accountNo

        result = await db.execute(select(User).where(User.email == email))
        existing_user = result.scalar_one_or_none()

        if existing_user:
            # 用户已存在，更新最后登录时间
            existing_user.last_login_date = datetime.now()
            await db.commit()
            logger.info(f"用户已存在，更新登录时间: {email}")
            return existing_user

        # 3. 创建新的本地用户
        user_data = convert_legacy_user_to_local_user(legacy_user)

        new_user = User(
            email=user_data["email"],
            password_hash=user_data["password_hash"],
            status=user_data["status"],
            role=user_data["role"],
            register_date=user_data["register_date"],
            last_login_date=user_data["last_login_date"],
            email_verified=user_data["email_verified"],
        )

        db.add(new_user)
        await db.commit()
        await db.refresh(new_user)

        logger.info(f"成功创建本地用户: {email}, 本地ID: {new_user.user_id}")
        return new_user

    except Exception as e:
        logger.error(f"Legacy登录处理失败: {str(e)}")
        await db.rollback()
        return None


# ========================================
# Legacy API 路由定义
# ========================================

# 创建Legacy API路由器
router = APIRouter()

# ========================================
# Legacy API 请求/响应模型
# ========================================


class LegacyAccountCheckRequest(BaseModel):
    """检查账号请求模型"""

    account: str


class LegacyLoginRequest(BaseModel):
    """Legacy登录请求模型"""

    account: str
    password: str
    remember: int = 1
    type: int = 0


class LegacyRegisterRequest(BaseModel):
    """Legacy注册请求模型"""

    account: str
    password: str
    nickname: Optional[str] = None
    email: Optional[str] = None


class LegacyApiResponseModel(BaseModel):
    """Legacy API统一响应模型"""

    msg: str
    code: int
    data: Any


# ========================================
# Legacy API 接口实现
# ========================================


@router.post(
    "/check/register",
    summary="检查账号是否已注册",
    description="检查指定账号是否在原网站已注册",
    response_model=LegacyApiResponseModel,
)
async def check_account_register(request: LegacyAccountCheckRequest):
    """
    检查账号是否已注册
    调用原网站API: POST https://www.00123.com/api/user/check/register
    """
    try:
        exists = await legacy_client.check_account_exists(request.account)

        return LegacyApiResponseModel(
            msg="查询成功",
            code=200,
            data=not exists,  # 原网站API：false表示已注册，true表示未注册
        )
    except Exception as e:
        logger.error(f"检查账号注册状态失败: {str(e)}")
        return LegacyApiResponseModel(msg=f"查询失败: {str(e)}", code=500, data=None)


@router.post(
    "/login",
    summary="原网站用户登录",
    description="通过原网站API验证用户登录，成功后在本地创建用户记录",
    response_model=AuthResponse,
)
async def legacy_login(request: LegacyLoginRequest, db: AsyncSession = Depends(get_db)):
    """
    原网站用户登录
    调用原网站API验证，成功后在本地创建用户记录并返回JWT令牌
    """
    try:
        # 调用Legacy登录逻辑
        user = await try_legacy_login(request.account, request.password, db)

        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="原网站登录验证失败",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 创建访问令牌
        from datetime import timedelta

        from app.core.config import settings

        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.email, "user_id": user.user_id},
            expires_delta=access_token_expires,
        )

        token = Token(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        )

        user_response = UserResponse.model_validate(user)

        return AuthResponse(
            success=True,
            message="Legacy登录成功，已自动创建本地账户",
            token=token,
            user=user_response.model_dump(),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Legacy登录失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Legacy登录处理失败: {str(e)}",
        )


@router.post(
    "/register",
    summary="原网站用户注册",
    description="调用原网站API注册新用户（保留接口，基本不使用）",
    response_model=LegacyApiResponseModel,
)
async def legacy_register(request: LegacyRegisterRequest):
    """
    原网站用户注册
    调用原网站API: POST https://www.00123.com/api/user/register
    注意：此接口保留但基本不使用，建议使用本地注册接口
    """
    try:
        # 这里可以实现调用原网站注册API的逻辑
        # 目前返回提示信息
        return LegacyApiResponseModel(
            msg="建议使用本地注册接口 /api/auth/register",
            code=200,
            data={
                "suggestion": "请使用新项目的注册接口",
                "local_register_endpoint": "/api/auth/register",
                "legacy_account": request.account,
            },
        )

    except Exception as e:
        logger.error(f"Legacy注册失败: {str(e)}")
        return LegacyApiResponseModel(msg=f"注册失败: {str(e)}", code=500, data=None)


@router.get(
    "/status",
    summary="Legacy Integration状态",
    description="查看Legacy Integration系统状态和配置信息",
)
async def legacy_status():
    """
    Legacy Integration状态查询
    返回系统状态和配置信息
    """
    try:
        # 测试原网站API连接
        test_result = await legacy_client.check_account_exists("test_connection")

        return {
            "service": "Legacy Integration",
            "status": "active",
            "original_website": {
                "base_url": LEGACY_API_BASE_URL,
                "connection": "ok" if test_result is not None else "failed",
                "timeout": legacy_client.timeout,
            },
            "features": {
                "account_check": "enabled",
                "legacy_login": "enabled",
                "auto_user_creation": "enabled",
                "legacy_register": "available_but_not_recommended",
            },
        }

    except Exception as e:
        logger.error(f"Legacy状态查询失败: {str(e)}")
        return {"service": "Legacy Integration", "status": "error", "error": str(e)}


# ========================================
# 说明文档
# ========================================

# 本模块实现了完全兼容RRH123原网站的API接口
#
# 已实现的接口：
# 1. POST /api/legacy/check/register          - 检查账号注册状态
# 2. POST /api/legacy/login                   - 原网站用户登录（推荐）
# 3. POST /api/legacy/register                - 原网站用户注册（保留但不推荐）
# 4. GET  /api/legacy/status                  - Legacy Integration状态查询
#
# 响应格式：
# - Legacy API: {"msg": "...", "code": 200, "data": ...}
# - 登录接口: 标准AuthResponse格式，包含JWT令牌
#
# 完全兼容原网站的数据结构和业务逻辑
