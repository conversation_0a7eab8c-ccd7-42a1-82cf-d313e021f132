"""
统一的时间处理工具模块
Unified datetime utilities module

确保整个系统使用一致的时间类型，避免 naive 和 timezone-aware datetime 混合使用导致的错误。
Ensures consistent datetime types throughout the system to avoid errors from mixing naive and timezone-aware datetimes.
"""

from datetime import datetime, timezone
from typing import Optional


def utc_now() -> datetime:
    """
    获取当前UTC时间（timezone-aware）
    Get current UTC time (timezone-aware)

    Returns:
        datetime: 当前UTC时间，包含时区信息
    """
    return datetime.now(timezone.utc)


def utc_timestamp() -> float:
    """
    获取当前UTC时间戳
    Get current UTC timestamp

    Returns:
        float: UTC时间戳
    """
    return utc_now().timestamp()


def to_utc(dt: datetime) -> datetime:
    """
    将datetime转换为UTC时间
    Convert datetime to UTC

    Args:
        dt: 要转换的datetime对象

    Returns:
        datetime: UTC时间（timezone-aware）
    """
    if dt.tzinfo is None:
        # 如果是naive datetime，假设它是UTC时间
        return dt.replace(tzinfo=timezone.utc)
    else:
        # 如果已经有时区信息，转换为UTC
        return dt.astimezone(timezone.utc)


def format_datetime(
    dt: Optional[datetime], format_str: str = "%Y-%m-%d %H:%M:%S UTC"
) -> Optional[str]:
    """
    格式化datetime为字符串
    Format datetime to string

    Args:
        dt: 要格式化的datetime对象
        format_str: 格式字符串

    Returns:
        str: 格式化后的时间字符串，如果dt为None则返回None
    """
    if dt is None:
        return None

    # 确保是UTC时间
    utc_dt = to_utc(dt)
    return utc_dt.strftime(format_str)


def parse_datetime(dt_str: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> datetime:
    """
    解析时间字符串为datetime对象
    Parse datetime string to datetime object

    Args:
        dt_str: 时间字符串
        format_str: 格式字符串

    Returns:
        datetime: 解析后的UTC时间（timezone-aware）
    """
    dt = datetime.strptime(dt_str, format_str)
    return dt.replace(tzinfo=timezone.utc)


def is_timezone_aware(dt: datetime) -> bool:
    """
    检查datetime是否包含时区信息
    Check if datetime is timezone-aware

    Args:
        dt: 要检查的datetime对象

    Returns:
        bool: True if timezone-aware, False if naive
    """
    return dt.tzinfo is not None and dt.tzinfo.utcoffset(dt) is not None


def ensure_timezone_aware(dt: datetime) -> datetime:
    """
    确保datetime是timezone-aware的
    Ensure datetime is timezone-aware

    Args:
        dt: datetime对象

    Returns:
        datetime: timezone-aware的datetime对象
    """
    if is_timezone_aware(dt):
        return dt
    else:
        # 假设naive datetime是UTC时间
        return dt.replace(tzinfo=timezone.utc)


# 为了向后兼容，提供一个统一的默认时间函数
def default_datetime() -> datetime:
    """
    默认时间函数，用于数据库模型的default参数
    Default datetime function for database model defaults

    Returns:
        datetime: 当前UTC时间（naive，兼容数据库）
    """
    # 为了兼容数据库，返回naive datetime
    # 但确保是UTC时间
    return datetime.utcnow()


# 常用的时间格式常量
class DateTimeFormats:
    """常用时间格式常量"""

    ISO_FORMAT = "%Y-%m-%dT%H:%M:%S.%fZ"
    SIMPLE_FORMAT = "%Y-%m-%d %H:%M:%S"
    DATE_ONLY = "%Y-%m-%d"
    TIME_ONLY = "%H:%M:%S"
    FILENAME_SAFE = "%Y%m%d_%H%M%S"


# 导出主要函数，方便其他模块导入
__all__ = [
    "utc_now",
    "utc_timestamp",
    "to_utc",
    "format_datetime",
    "parse_datetime",
    "is_timezone_aware",
    "ensure_timezone_aware",
    "default_datetime",
    "DateTimeFormats",
]
