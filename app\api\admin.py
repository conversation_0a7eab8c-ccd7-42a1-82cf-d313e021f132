"""
管理员API路由
支持混合模式管理员系统
"""

import logging
from typing import List, Optional, Union

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from pydantic import BaseModel, EmailStr, Field
from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.admin import (
    AdminAuthService,
    AdminPermissions,
    get_admin_info,
    get_current_admin,
    has_permission,
    require_admin,
    require_super_admin,
)
from app.core.security import create_access_token, get_current_user_from_token
from app.db.database import get_db
from app.models.user import User, UserRole, UserStatus
from app.schemas.common import (
    ServiceStatusInfo,
    ServiceStatusRequest,
    ServiceStatusResponse,
)
from app.schemas.user import UserResponse

logger = logging.getLogger(__name__)
router = APIRouter()


# ========================================
# Pydantic 模式定义
# ========================================


class AdminLoginRequest(BaseModel):
    """管理员登录请求"""

    email: EmailStr
    password: str


class AdminLoginResponse(BaseModel):
    """管理员登录响应"""

    access_token: str
    token_type: str = "bearer"
    admin_info: dict


class CreateAdminRequest(BaseModel):
    """创建管理员请求"""

    email: EmailStr
    password: str
    role: UserRole = UserRole.ADMIN


class CreateUserRequest(BaseModel):
    """创建普通用户请求"""

    email: EmailStr
    password: str = Field(..., min_length=8, description="Password must be at least 8 characters")
    nickname: Optional[str] = Field(None, max_length=50, description="User nickname")
    role: UserRole = Field(default=UserRole.USER, description="User role")
    status: UserStatus = Field(default=UserStatus.ACTIVE, description="User status")
    email_verified: bool = Field(default=True, description="Email verification status")


class AdminInfoResponse(BaseModel):
    """管理员信息响应"""

    user_id: int
    email: str
    role: str
    status: str
    is_super_admin: bool
    source: str
    register_date: Optional[str] = None
    last_login_date: Optional[str] = None


class SystemStatsResponse(BaseModel):
    """系统统计响应"""

    total_users: int
    active_users: int
    total_admins: int
    total_products: int
    total_keys: int
    active_keys: int


# ========================================
# 管理员认证端点
# ========================================


@router.post("/login", response_model=AdminLoginResponse, operation_id="admin_login")
async def admin_login(
    login_data: AdminLoginRequest, db: AsyncSession = Depends(get_db)
):
    """
    管理员登录
    支持超级管理员（环境变量）和数据库管理员
    """
    try:
        admin_user = await AdminAuthService.authenticate_admin(
            login_data.email, login_data.password, db
        )

        if not admin_user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password, or insufficient permissions",
            )

        # 创建访问令牌
        if isinstance(admin_user, dict):
            # 超级管理员
            token_data = {"sub": admin_user["email"], "role": "super_admin"}
        else:
            # 数据库管理员
            token_data = {
                "sub": admin_user.email,
                "user_id": admin_user.user_id,
                "role": admin_user.role.value,
            }

        access_token = create_access_token(data=token_data)

        return AdminLoginResponse(
            access_token=access_token, admin_info=get_admin_info(admin_user)
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Admin login failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error occurred during login process",
        )


@router.post("/token", operation_id="admin_token")
async def admin_token(
    form_data: OAuth2PasswordRequestForm = Depends(), db: AsyncSession = Depends(get_db)
):
    """
    管理员OAuth2密码认证端点
    支持Swagger UI的用户名密码认证
    """
    try:
        admin_user = await AdminAuthService.authenticate_admin(
            form_data.username,  # OAuth2使用username字段，这里对应email
            form_data.password,
            db,
        )

        if not admin_user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password, or insufficient permissions",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 创建访问令牌
        if isinstance(admin_user, dict):
            # 超级管理员
            token_data = {"sub": admin_user["email"], "role": "super_admin"}
        else:
            # 数据库管理员
            token_data = {
                "sub": admin_user.email,
                "user_id": admin_user.user_id,
                "role": admin_user.role.value,
            }

        access_token = create_access_token(data=token_data)

        return {"access_token": access_token, "token_type": "bearer"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Admin token authentication failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error occurred during authentication process",
        )


@router.get("/info", response_model=AdminInfoResponse, operation_id="admin_info")
async def get_admin_info_endpoint(
    current_admin: Union[User, dict] = Depends(get_current_admin),
):
    """获取当前管理员信息"""
    return get_admin_info(current_admin)


@router.get("/me", response_model=AdminInfoResponse, operation_id="admin_me")
async def get_admin_me(
    current_admin: Union[User, dict] = Depends(get_current_user_from_token),
):
    """
    获取当前管理员信息（OAuth2密码认证）
    此端点使用OAuth2PasswordBearer，支持用户名密码认证
    """
    if not current_admin:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Admin authentication required",
        )

    return get_admin_info(current_admin)


# ========================================
# 管理员管理端点（仅超级管理员）
# ========================================


@router.post("/create", response_model=UserResponse, operation_id="admin_create")
async def create_admin(
    admin_data: CreateAdminRequest,
    db: AsyncSession = Depends(get_db),
    current_admin: Union[User, dict] = Depends(require_super_admin()),
):
    """
    创建管理员用户（仅超级管理员）
    """
    try:
        # 检查权限
        if not has_permission(current_admin, AdminPermissions.ADMIN_MANAGE):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions: Only super admin can create admin users",
            )

        new_admin = await AdminAuthService.create_admin_user(
            email=admin_data.email,
            password=admin_data.password,
            role=admin_data.role,
            db=db,
        )

        logger.info(
            f"Admin {get_admin_info(current_admin)['email']} created new admin: {new_admin.email}"
        )

        return UserResponse.model_validate(new_admin)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create admin: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error occurred during admin creation process",
        )


@router.get("/list", response_model=List[UserResponse], operation_id="admin_list")
async def list_admins(
    db: AsyncSession = Depends(get_db),
    current_admin: Union[User, dict] = Depends(require_admin()),
):
    """获取管理员列表"""
    try:
        result = await db.execute(
            select(User)
            .where(User.role.in_([UserRole.ADMIN, UserRole.SUPER_ADMIN]))
            .order_by(User.register_date.desc())
        )
        admins = result.scalars().all()

        return [UserResponse.model_validate(admin) for admin in admins]

    except Exception as e:
        logger.error(f"Failed to get admin list: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get admin list",
        )


# ========================================
# 系统管理端点
# ========================================


@router.get("/stats", response_model=SystemStatsResponse, operation_id="admin_stats")
async def get_system_stats(
    db: AsyncSession = Depends(get_db),
    current_admin: Union[User, dict] = Depends(require_admin()),
):
    """获取系统统计信息"""
    try:
        # 检查权限
        if not has_permission(current_admin, AdminPermissions.SYSTEM_STATS):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions: Cannot view system statistics",
            )

        # 用户统计
        total_users_result = await db.execute(select(func.count(User.user_id)))
        total_users = total_users_result.scalar()

        active_users_result = await db.execute(
            select(func.count(User.user_id)).where(User.status == UserStatus.ACTIVE)
        )
        active_users = active_users_result.scalar()

        # 管理员统计
        total_admins_result = await db.execute(
            select(func.count(User.user_id)).where(
                User.role.in_([UserRole.ADMIN, UserRole.SUPER_ADMIN])
            )
        )
        total_admins = total_admins_result.scalar()

        # 产品和密钥统计（如果表存在）
        total_products = 0
        total_keys = 0
        active_keys = 0

        try:
            from app.models.key import Key
            from app.models.product import Product

            products_result = await db.execute(select(func.count(Product.product_id)))
            total_products = products_result.scalar()

            keys_result = await db.execute(select(func.count(Key.key_id)))
            total_keys = keys_result.scalar()

            # 这里可以添加活跃密钥的统计逻辑
            active_keys = total_keys  # 简化处理

        except Exception as e:
            logger.warning(f"Failed to get product/key statistics: {e}")

        return SystemStatsResponse(
            total_users=total_users or 0,
            active_users=active_users or 0,
            total_admins=total_admins or 0,
            total_products=total_products or 0,
            total_keys=total_keys or 0,
            active_keys=active_keys or 0,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get system statistics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get system statistics",
        )


# ========================================
# 用户管理端点
# ========================================


@router.get(
    "/users", response_model=List[UserResponse], operation_id="admin_users_list"
)
async def list_users(
    skip: int = 0,
    limit: int = 50,
    db: AsyncSession = Depends(get_db),
    current_admin: Union[User, dict] = Depends(require_admin()),
):
    """获取用户列表（使用隔离连接避免并发冲突）"""
    try:
        # 检查权限
        if not has_permission(current_admin, AdminPermissions.USER_VIEW):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions: Cannot view user list",
            )

        # 使用隔离连接避免"another operation is in progress"错误
        from app.db.database import execute_isolated_query

        query = """
        SELECT user_id, email, password_hash, status, role, register_date,
               last_login_date, nickname, email_verified, email_verification_code,
               email_verification_expires, remarks
        FROM users
        ORDER BY register_date DESC
        LIMIT $1 OFFSET $2
        """

        rows = await execute_isolated_query(query, (limit, skip))

        # 转换为UserResponse对象
        users = []
        for row in rows:
            user_data = {
                "user_id": row["user_id"],
                "email": row["email"],
                "status": row["status"],
                "role": row["role"],
                "register_date": row["register_date"],
                "last_login_date": row["last_login_date"],
                "nickname": row["nickname"],
                "email_verified": row["email_verified"],
                "remarks": row["remarks"]
            }
            users.append(UserResponse(**user_data))

        return users

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get user list: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user list",
        )


@router.post("/users", response_model=UserResponse, operation_id="admin_create_user")
async def create_user(
    user_data: CreateUserRequest,
    db: AsyncSession = Depends(get_db),
    current_admin: Union[User, dict] = Depends(require_admin()),
):
    """
    创建普通用户（管理员权限）
    """
    try:
        # 检查权限
        if not has_permission(current_admin, AdminPermissions.USER_CREATE):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions: Cannot create users",
            )

        # 检查邮箱是否已存在
        existing_user_result = await db.execute(
            select(User).where(User.email == user_data.email)
        )
        if existing_user_result.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )

        # 创建用户
        from app.core.security import get_password_hash
        from datetime import datetime

        hashed_password = get_password_hash(user_data.password)

        new_user = User(
            email=user_data.email,
            password_hash=hashed_password,
            nickname=user_data.nickname,
            role=user_data.role,
            status=user_data.status,
            email_verified=user_data.email_verified,
            register_date=datetime.now()
        )

        db.add(new_user)
        await db.commit()
        await db.refresh(new_user)

        admin_info = get_admin_info(current_admin)
        logger.info(
            f"Admin {admin_info['email']} created new user: {new_user.email}"
        )

        return UserResponse.model_validate(new_user)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error occurred during user creation process",
        )


@router.put("/users/{user_id}/status", operation_id="admin_update_user_status")
async def update_user_status(
    user_id: int,
    new_status: UserStatus,
    db: AsyncSession = Depends(get_db),
    current_admin: Union[User, dict] = Depends(require_admin()),
):
    """更新用户状态（激活/封禁）"""
    try:
        # 检查权限
        if not has_permission(current_admin, AdminPermissions.USER_BAN):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions: Cannot modify user status",
            )

        # 查找用户
        result = await db.execute(select(User).where(User.user_id == user_id))
        user = result.scalar_one_or_none()

        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )

        # Prevent modifying admin status (unless super admin)
        if user.role in [UserRole.ADMIN, UserRole.SUPER_ADMIN]:
            if not has_permission(current_admin, AdminPermissions.ADMIN_MANAGE):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Insufficient permissions: Cannot modify admin status",
                )

        # Update status
        user.status = new_status
        await db.commit()

        admin_info = get_admin_info(current_admin)
        logger.info(
            f"Admin {admin_info['email']} updated user {user.email} status to: {new_status}"
        )

        return {"message": f"User status updated to: {new_status.value}"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update user status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user status",
        )


@router.delete("/users/{user_id}", operation_id="admin_delete_user")
async def delete_user(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    current_admin: Union[User, dict] = Depends(require_super_admin()),
):
    """删除用户（仅超级管理员）"""
    try:
        # 检查权限
        if not has_permission(current_admin, AdminPermissions.USER_DELETE):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions: Only super admin can delete users",
            )

        # 查找用户
        result = await db.execute(select(User).where(User.user_id == user_id))
        user = result.scalar_one_or_none()

        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )

        # 防止删除超级管理员
        if user.role == UserRole.SUPER_ADMIN:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot delete super admin user"
            )

        # 防止删除当前登录的管理员
        current_admin_info = get_admin_info(current_admin)
        if user.email == current_admin_info.get('email'):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot delete yourself"
            )

        # 记录删除操作
        user_email = user.email
        user_role = user.role.value

        # 删除用户
        await db.delete(user)
        await db.commit()

        logger.warning(f"Admin {current_admin_info['email']} deleted user: {user_email} (role: {user_role})")

        return {"message": f"User {user_email} has been permanently deleted"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete user",
        )


# ========================================
# 服务状态管理端点
# ========================================


@router.get(
    "/service-status",
    response_model=ServiceStatusInfo,
    operation_id="admin_get_service_status",
)
async def get_service_status(
    current_admin: Union[User, dict] = Depends(require_admin()),
):
    """获取当前服务状态"""
    from datetime import datetime

    from app.core.config import settings

    try:
        # 检查权限
        if not has_permission(current_admin, AdminPermissions.SYSTEM_STATS):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions: Cannot view service status",
            )

        return ServiceStatusInfo(
            current_mode=settings.USER_ACCESS_MODE,
            current_mode_number=settings.get_access_mode_number(),
            current_mode_description=settings.get_access_mode_description(),
            user_registration_allowed=settings.allow_user_registration,
            user_login_allowed=settings.allow_user_login,
            available_modes=settings.get_all_access_modes(),
            timestamp=datetime.now(),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get service status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get service status",
        )


# ========================================
# 数据库管理端点
# ========================================


class DatabaseInitRequest(BaseModel):
    """数据库初始化请求"""

    force_recreate: bool = False  # 是否强制重新创建表 (暂未实现，防止数据丢失)
    create_sample_data: bool = False  # 是否创建示例数据
    confirm_operation: bool = False  # 确认操作（安全检查）


class DatabaseInitResponse(BaseModel):
    """数据库初始化响应"""

    success: bool
    message: str
    details: dict
    tables_created: List[str]
    warnings: List[str] = []
    timestamp: str


@router.post(
    "/database/init",
    response_model=DatabaseInitResponse,
    operation_id="admin_init_database",
)
async def initialize_database(
    init_request: DatabaseInitRequest,
    db: AsyncSession = Depends(get_db),
    current_admin: Union[User, dict] = Depends(require_super_admin()),
):
    """
    初始化数据库表结构（仅超级管理员）

    这是一个安全的数据库初始化端点，具有以下特性：
    - 只有超级管理员可以访问
    - 幂等操作，可以安全地重复执行
    - 详细的操作日志和反馈
    - 可选的示例数据创建
    """
    from datetime import datetime

    try:
        # 检查权限
        if not has_permission(current_admin, AdminPermissions.ADMIN_MANAGE):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions: Only super admin can initialize database",
            )

        # 安全确认检查
        if not init_request.confirm_operation:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Database initialization requires explicit confirmation. Set 'confirm_operation' to true.",
            )

        # 防止数据丢失的安全检查
        if init_request.force_recreate:
            raise HTTPException(
                status_code=status.HTTP_501_NOT_IMPLEMENTED,
                detail="force_recreate is not implemented to prevent accidental data loss. Only safe table creation is supported.",
            )

        admin_info = get_admin_info(current_admin)
        logger.warning(
            f"🔧 Database initialization requested by super admin: {admin_info['email']}"
        )

        # 获取数据库信息
        from app.db.database import engine, get_database_info, init_db

        db_info = get_database_info()
        if not db_info["engine_available"]:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Database engine not available. Please check database configuration.",
            )

        warnings = []
        tables_created = []

        # 检查现有表 - 使用专用连接避免并发冲突
        existing_tables = []
        try:
            from sqlalchemy import text
            from sqlalchemy.ext.asyncio import create_async_engine
            from app.core.config import settings
            from app.db.database import convert_to_async_url

            # 创建专用查询引擎
            database_url = settings.get_effective_database_url()
            async_database_url = convert_to_async_url(database_url)

            query_config = {
                "echo": False,
                "pool_size": 1,
                "max_overflow": 0,
                "pool_timeout": 30,
                "connect_args": {
                    "statement_cache_size": 0,
                    "command_timeout": 30,
                    "application_name": "FastAuth_Table_Check",
                },
            }

            query_engine = create_async_engine(async_database_url, **query_config)

            try:
                # 使用专用引擎查询现有表
                async with query_engine.connect() as conn:
                    # 查询现有表
                    if db_info["database_type"] == "postgresql":
                        result = await conn.execute(
                            text(
                                "SELECT tablename FROM pg_tables WHERE schemaname = 'public'"
                            )
                        )
                    elif db_info["database_type"] == "mysql":
                        result = await conn.execute(text("SHOW TABLES"))
                    elif db_info["database_type"] == "sqlite":
                        result = await conn.execute(
                            text("SELECT name FROM sqlite_master WHERE type='table'")
                        )
                    else:
                        result = []

                    existing_tables = [row[0] for row in result.fetchall()]
                    logger.info(f"🔍 检查到现有表: {existing_tables}")
            finally:
                await query_engine.dispose()

        except Exception as e:
            logger.warning(f"Could not check existing tables: {e}")
            warnings.append(f"Could not check existing tables: {str(e)}")

        # 执行数据库初始化
        try:
            await init_db()

            # 获取所有定义的表名
            from app.db.database import Base

            all_tables = list(Base.metadata.tables.keys())

            # 确定哪些表是新创建的
            new_tables = [table for table in all_tables if table not in existing_tables]
            existing_tables_kept = [
                table for table in all_tables if table in existing_tables
            ]

            tables_created = new_tables

            if existing_tables_kept:
                warnings.append(
                    f"Existing tables preserved: {', '.join(existing_tables_kept)}"
                )

            logger.info(
                f"✅ Database initialization completed. Tables: {', '.join(all_tables)}"
            )

        except Exception as e:
            logger.error(f"❌ Database initialization failed: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database initialization failed: {str(e)}",
            )

        # 可选：创建示例数据
        sample_data_created = False
        if init_request.create_sample_data:
            try:
                await _create_sample_data(db, current_admin)
                sample_data_created = True
                logger.info("✅ Sample data created successfully")
            except Exception as e:
                logger.warning(f"⚠️ Sample data creation failed: {e}")
                warnings.append(f"Sample data creation failed: {str(e)}")

        # 构建响应
        details = {
            "database_type": db_info["database_type"],
            "total_tables": len(Base.metadata.tables),
            "new_tables_created": len(tables_created),
            "existing_tables_preserved": len(existing_tables) - len(tables_created)
            if existing_tables
            else 0,
            "sample_data_created": sample_data_created,
            "force_recreate_requested": init_request.force_recreate,
            "admin_email": admin_info["email"],
        }

        success_message = f"Database initialization completed successfully. Created {len(tables_created)} new tables."
        if warnings:
            success_message += f" {len(warnings)} warnings occurred."

        return DatabaseInitResponse(
            success=True,
            message=success_message,
            details=details,
            tables_created=tables_created,
            warnings=warnings,
            timestamp=datetime.now().isoformat(),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during database initialization: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error during database initialization: {str(e)}",
        )


async def _create_sample_data(db: AsyncSession, current_admin: Union[User, dict]):
    """创建示例数据（内部函数）"""
    try:
        # 检查是否已有管理员用户（避免重复创建）
        result = await db.execute(
            select(User).where(User.role.in_([UserRole.ADMIN, UserRole.SUPER_ADMIN]))
        )
        existing_admins = result.scalars().all()

        if not existing_admins:
            # 创建示例管理员（如果不存在）
            from app.core.admin import AdminAuthService
            from app.core.config import settings

            try:
                await AdminAuthService.create_admin_user(
                    email=settings.SUPER_ADMIN_EMAIL,
                    password=settings.SUPER_ADMIN_PASSWORD,
                    role=UserRole.SUPER_ADMIN,
                    db=db,
                )
                logger.info(f"Created sample super admin: {settings.SUPER_ADMIN_EMAIL}")
            except Exception as e:
                # 如果管理员已存在，忽略错误
                if "already exists" in str(e).lower() or "duplicate" in str(e).lower():
                    logger.info("Super admin already exists, skipping creation")
                else:
                    raise

        # 可以在这里添加更多示例数据创建逻辑
        # 例如：示例产品、示例用户等

    except Exception as e:
        logger.error(f"Failed to create sample data: {e}")
        raise


@router.get("/debug/config", operation_id="admin_debug_config")
async def get_debug_config(
    current_admin: Union[User, dict] = Depends(require_admin()),
):
    """获取调试配置信息（仅超级管理员）"""
    try:
        # 检查权限 - 仅超级管理员可访问调试信息
        if not has_permission(current_admin, AdminPermissions.SYSTEM_CONFIG):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions: Only super admin can access debug info",
            )

        import os
        from datetime import datetime
        from app.core.config import settings
        from app.db.database import database_type, engine

        # 环境检测
        is_vercel = os.getenv("VERCEL") == "1"
        database_url = os.getenv("DATABASE_URL", "") or os.getenv("POSTGRES_URL", "")
        is_supabase = "supabase" in database_url.lower() or "pooler" in database_url.lower()

        # 数据库引擎信息
        engine_info = {}
        if engine:
            engine_info = {
                "pool_size": getattr(engine.pool, 'size', 'unknown'),
                "max_overflow": getattr(engine.pool, 'overflow', 'unknown'),
                "pool_timeout": getattr(engine.pool, 'timeout', 'unknown'),
                "url_scheme": str(engine.url).split('://')[0] if engine.url else 'unknown',
            }

        debug_info = {
            "environment": {
                "VERCEL": os.getenv("VERCEL"),
                "is_vercel": is_vercel,
                "is_supabase": is_supabase,
                "database_type": database_type,
                "config_source": settings._get_database_config_source(),
            },
            "database_config": {
                "has_DATABASE_URL": bool(os.getenv("DATABASE_URL")),
                "has_POSTGRES_URL": bool(os.getenv("POSTGRES_URL")),
                "database_url_prefix": database_url[:50] + "..." if database_url else "None",
                "effective_url_prefix": settings.get_effective_database_url()[:50] + "..." if settings.get_effective_database_url() else "None",
            },
            "engine_info": engine_info,
            "timestamp": datetime.now().isoformat(),
        }

        return {"debug_info": debug_info}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get debug config: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error occurred during debug config retrieval",
        )


@router.get("/database/status", operation_id="admin_database_status")
async def get_database_status(
    current_admin: Union[User, dict] = Depends(require_admin()),
):
    """
    获取数据库状态信息
    """
    try:
        # 检查权限
        if not has_permission(current_admin, AdminPermissions.SYSTEM_STATS):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions: Cannot view database status",
            )

        from datetime import datetime

        from app.db.database import Base, engine, get_database_info

        db_info = get_database_info()

        # 检查表状态 - 使用专用连接避免并发冲突
        tables_status = {}
        if db_info["engine_available"]:
            try:
                from sqlalchemy import text
                from sqlalchemy.ext.asyncio import create_async_engine
                from app.core.config import settings
                from app.db.database import convert_to_async_url, Base

                # 创建专用状态检查引擎
                database_url = settings.get_effective_database_url()
                async_database_url = convert_to_async_url(database_url)

                status_config = {
                    "echo": False,
                    "pool_size": 1,
                    "max_overflow": 0,
                    "pool_timeout": 30,
                    "connect_args": {
                        "statement_cache_size": 0,
                        "command_timeout": 30,
                        "application_name": "FastAuth_Status_Check",
                    },
                }

                status_engine = create_async_engine(async_database_url, **status_config)

                try:
                    # 使用专用引擎检查表状态
                    async with status_engine.connect() as conn:
                        # 获取所有定义的表
                        defined_tables = list(Base.metadata.tables.keys())

                        for table_name in defined_tables:
                            try:
                                # 检查表是否存在并获取行数
                                result = await conn.execute(
                                    text(f"SELECT COUNT(*) FROM {table_name}")
                                )
                                row_count = result.scalar()
                                tables_status[table_name] = {
                                    "exists": True,
                                    "row_count": row_count,
                                    "status": "healthy",
                                }
                                logger.debug(f"✅ 表 {table_name}: {row_count} 行")
                            except Exception as e:
                                tables_status[table_name] = {
                                    "exists": False,
                                    "row_count": 0,
                                    "status": "missing",
                                    "error": str(e),
                                }
                                logger.debug(f"❌ 表 {table_name}: {str(e)}")
                finally:
                    await status_engine.dispose()

            except Exception as e:
                logger.warning(f"Could not check table status: {e}")

        return {
            "database_info": db_info,
            "tables_status": tables_status,
            "total_defined_tables": len(Base.metadata.tables),
            "tables_existing": len(
                [t for t in tables_status.values() if t.get("exists", False)]
            ),
            "tables_missing": len(
                [t for t in tables_status.values() if not t.get("exists", False)]
            ),
            "timestamp": datetime.now().isoformat(),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get database status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get database status: {str(e)}",
        )


@router.post(
    "/service-status",
    response_model=ServiceStatusResponse,
    operation_id="admin_set_service_status",
)
async def set_service_status(
    request: ServiceStatusRequest,
    current_admin: Union[User, dict] = Depends(require_admin()),
):
    """设置服务状态（管理员专用）"""
    from datetime import datetime

    from app.core.config import settings

    try:
        # 检查权限
        if not has_permission(current_admin, AdminPermissions.ADMIN_MANAGE):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions: Only super admin can modify service status",
            )

        # 验证并设置访问模式
        if not settings.set_access_mode(request.access_mode):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid access mode: {request.access_mode}. Supported modes: 1/open, 2/login_only, 3/restricted",
            )

        admin_info = get_admin_info(current_admin)
        logger.info(
            f"Admin {admin_info['email']} changed service status to: {settings.get_access_mode_description()}"
        )

        return ServiceStatusResponse(
            success=True,
            message=f"Service status updated to: {settings.get_access_mode_description()}",
            current_mode=settings.USER_ACCESS_MODE,
            current_mode_number=settings.get_access_mode_number(),
            current_mode_description=settings.get_access_mode_description(),
            user_registration_allowed=settings.allow_user_registration,
            user_login_allowed=settings.allow_user_login,
            timestamp=datetime.now(),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to set service status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to set service status",
        )
