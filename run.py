#!/usr/bin/env python3
"""
FastAPI 开发模式启动脚本
"""

import subprocess
import sys
from pathlib import Path


def start_development_server():
    host = "127.0.0.1"
    port = 8000

    # 获取项目根目录
    project_root = Path(__file__).parent

    # 检查虚拟环境
    venv_python = project_root / "venv" / "Scripts" / "python.exe"
    if venv_python.exists():
        python_executable = str(venv_python)
        print(f"✅ 使用虚拟环境: {python_executable}")
    else:
        python_executable = sys.executable
        print(f"⚠️  使用系统Python: {python_executable}")

    try:
        # 首先尝试使用 fastapi dev 命令
        try:
            subprocess.run(
                [
                    python_executable,
                    "-m",
                    "fastapi",
                    "dev",
                    "main.py",
                    "--host",
                    host,
                    "--port",
                    str(port),
                ],
                check=True,
                cwd=project_root,
            )
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️  fastapi dev 命令失败，尝试使用 uvicorn...")
            # 如果 fastapi dev 失败，回退到 uvicorn
            subprocess.run(
                [
                    python_executable,
                    "-m",
                    "uvicorn",
                    "main:app",
                    "--host",
                    host,
                    "--port",
                    str(port),
                    "--reload",
                ],
                check=True,
                cwd=project_root,
            )
    except KeyboardInterrupt:
        print("\n\n👋 开发服务器已停止 \n")
        sys.exit(0)


def main():
    """主函数"""

    print("FastAPI 开发模式启动器")
    print("-" * 50)
    # 启动开发服务器
    start_development_server()


if __name__ == "__main__":
    main()
