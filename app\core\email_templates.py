"""
邮件模板管理器
支持多种类型的邮件模板：验证码、提醒、营销等
"""

import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict

logger = logging.getLogger(__name__)


class EmailTemplateManager:
    """邮件模板管理器"""

    def __init__(self):
        self.template_dir = Path(__file__).parent.parent / "email" / "templates"
        self.default_vars = {
            "service_name": "MetaTrader Auth Support",
            "support_email": "<EMAIL>",
            "company_address": "MetaTrader Auth Support Team",
            "current_year": datetime.now().year,
        }

    def _load_template(self, category: str, template_name: str) -> str:
        """加载模板文件"""
        template_path = self.template_dir / category / f"{template_name}.html"

        if not template_path.exists():
            logger.error(f"模板文件不存在: {template_path}")
            raise FileNotFoundError(f"Template not found: {template_path}")

        try:
            with open(template_path, encoding="utf-8") as f:
                return f.read()
        except Exception as e:
            logger.error(f"读取模板文件失败: {template_path}, 错误: {str(e)}")
            raise

    def _render_template(self, template_content: str, variables: Dict[str, Any]) -> str:
        """渲染模板"""
        # 合并默认变量和传入变量
        all_vars = {**self.default_vars, **variables}

        try:
            # 使用简单的字符串替换（可以后续升级为Jinja2）
            rendered = template_content
            for key, value in all_vars.items():
                placeholder = f"{{{{{key}}}}}"
                rendered = rendered.replace(placeholder, str(value))

            return rendered
        except Exception as e:
            logger.error(f"模板渲染失败: {str(e)}")
            raise

    # ========== 验证码类邮件 ==========

    def get_email_verification_template(
        self, verification_code: str, **kwargs
    ) -> tuple[str, str]:
        """获取邮箱验证码模板"""
        variables = {"verification_code": verification_code, **kwargs}

        template_content = self._load_template("code", "email_verification")
        html_content = self._render_template(template_content, variables)

        # 生成纯文本版本
        text_content = f"""
[{self.default_vars["service_name"]}] 邮箱验证码

尊敬的用户，您好！

感谢您注册我们的服务。请使用以下验证码完成邮箱验证：

验证码：{verification_code}

重要提醒：
- 验证码有效期为 15分钟
- 请勿将验证码告诉他人
- 如果您没有注册账户，请忽略此邮件

此邮件由系统自动发送，请勿回复。
如有疑问，请联系客服：{self.default_vars["support_email"]}

© {self.default_vars["current_year"]} {self.default_vars["service_name"]}. All rights reserved.
        """

        return html_content, text_content.strip()

    def get_password_reset_template(
        self, verification_code: str, **kwargs
    ) -> tuple[str, str]:
        """获取密码重置验证码模板"""
        variables = {"verification_code": verification_code, **kwargs}

        template_content = self._load_template("code", "password_reset")
        html_content = self._render_template(template_content, variables)

        text_content = f"""
[{self.default_vars["service_name"]}] 密码重置验证码

密码重置请求

我们收到了您的密码重置请求。请使用以下验证码来重置您的密码：

验证码：{verification_code}

安全提醒：
- 验证码有效期为 10分钟
- 如果您没有请求重置密码，请立即联系客服
- 请勿将验证码告诉任何人

此邮件由系统自动发送，请勿回复。
如有疑问，请联系客服：{self.default_vars["support_email"]}

© {self.default_vars["current_year"]} {self.default_vars["service_name"]}. All rights reserved.
        """

        return html_content, text_content.strip()

    def get_two_factor_auth_template(
        self, verification_code: str, login_info: Dict[str, str], **kwargs
    ) -> tuple[str, str]:
        """获取双因素认证验证码模板"""
        variables = {
            "verification_code": verification_code,
            "login_time": login_info.get("login_time", "未知"),
            "login_ip": login_info.get("login_ip", "未知"),
            "device_info": login_info.get("device_info", "未知"),
            **kwargs,
        }

        template_content = self._load_template("code", "two_factor_auth")
        html_content = self._render_template(template_content, variables)

        text_content = f"""
[{self.default_vars["service_name"]}] 双因素认证验证码

安全登录验证

检测到您正在尝试登录账户。为了保护您的账户安全，请使用以下验证码完成双因素认证：

验证码：{verification_code}

登录信息：
- 登录时间：{variables["login_time"]}
- 登录IP：{variables["login_ip"]}
- 设备信息：{variables["device_info"]}
- 验证码有效期：5分钟

安全提醒：
- 如果不是您本人操作，请立即修改密码
- 请勿将验证码告诉任何人

此邮件由系统自动发送，请勿回复。
如有疑问，请联系客服：{self.default_vars["support_email"]}

© {self.default_vars["current_year"]} {self.default_vars["service_name"]}. All rights reserved.
        """

        return html_content, text_content.strip()

    # ========== 提醒类邮件 ==========

    def get_login_alert_template(
        self, user_name: str, login_info: Dict[str, str], **kwargs
    ) -> tuple[str, str]:
        """获取登录提醒模板"""
        variables = {
            "user_name": user_name,
            "login_time": login_info.get("login_time", "未知"),
            "login_ip": login_info.get("login_ip", "未知"),
            "login_location": login_info.get("login_location", "未知"),
            "device_type": login_info.get("device_type", "未知"),
            "browser_info": login_info.get("browser_info", "未知"),
            "security_center_url": kwargs.get("security_center_url", "#"),
            **kwargs,
        }

        template_content = self._load_template("notification", "login_alert")
        html_content = self._render_template(template_content, variables)

        text_content = f"""
[{self.default_vars["service_name"]}] 登录提醒

您好，{user_name}！

我们检测到您的账户有新的登录活动。如果这是您本人操作，请忽略此邮件。

登录详情：
- 登录时间：{variables["login_time"]}
- 登录IP：{variables["login_ip"]}
- 登录地点：{variables["login_location"]}
- 设备类型：{variables["device_type"]}
- 浏览器：{variables["browser_info"]}

安全建议：
- 如果不是您本人登录，请立即修改密码
- 启用双因素认证增强账户安全
- 定期检查账户活动记录

此邮件由系统自动发送，请勿回复。
如有疑问，请联系客服：{self.default_vars["support_email"]}

© {self.default_vars["current_year"]} {self.default_vars["service_name"]}. All rights reserved.
        """

        return html_content, text_content.strip()

    def get_password_changed_template(
        self, user_name: str, change_info: Dict[str, str], **kwargs
    ) -> tuple[str, str]:
        """获取密码修改通知模板"""
        variables = {
            "user_name": user_name,
            "change_time": change_info.get("change_time", "未知"),
            "change_ip": change_info.get("change_ip", "未知"),
            "change_location": change_info.get("change_location", "未知"),
            "device_type": change_info.get("device_type", "未知"),
            "security_center_url": kwargs.get("security_center_url", "#"),
            "contact_support_url": kwargs.get("contact_support_url", "#"),
            **kwargs,
        }

        template_content = self._load_template("notification", "password_changed")
        html_content = self._render_template(template_content, variables)

        text_content = f"""
[{self.default_vars["service_name"]}] 密码修改通知

您好，{user_name}！

您的账户密码已成功修改。如果这是您本人操作，请忽略此邮件。

修改详情：
- 修改时间：{variables["change_time"]}
- 操作IP：{variables["change_ip"]}
- 操作地点：{variables["change_location"]}
- 设备类型：{variables["device_type"]}

重要提醒：
如果不是您本人操作，您的账户可能已被盗用，请立即联系客服。

此邮件由系统自动发送，请勿回复。
如有疑问，请联系客服：{self.default_vars["support_email"]}

© {self.default_vars["current_year"]} {self.default_vars["service_name"]}. All rights reserved.
        """

        return html_content, text_content.strip()

    # ========== 营销类邮件 ==========

    def get_welcome_template(self, user_name: str, **kwargs) -> tuple[str, str]:
        """获取欢迎邮件模板"""
        variables = {
            "user_name": user_name,
            "dashboard_url": kwargs.get("dashboard_url", "#"),
            "support_chat_url": kwargs.get("support_chat_url", "#"),
            "support_phone": kwargs.get("support_phone", "************"),
            "unsubscribe_url": kwargs.get("unsubscribe_url", "#"),
            "privacy_policy_url": kwargs.get("privacy_policy_url", "#"),
            **kwargs,
        }

        template_content = self._load_template("marketing", "welcome")
        html_content = self._render_template(template_content, variables)

        text_content = f"""
[{self.default_vars["service_name"]}] 欢迎加入

您好，{user_name}！

恭喜您成功注册 {self.default_vars["service_name"]}！我们很高兴您加入我们的大家庭。

精彩功能等您探索：
- 快速启动：一键开始您的旅程
- 安全保障：企业级安全防护
- 专业服务：7×24小时支持

新手指南：
1. 完善个人资料 - 让我们更好地为您服务
2. 探索核心功能 - 发现平台的强大能力
3. 加入社区讨论 - 与其他用户交流经验
4. 关注最新动态 - 获取第一手资讯

需要帮助？
邮箱：{self.default_vars["support_email"]}
客服热线：{variables["support_phone"]}

感谢您选择 {self.default_vars["service_name"]}，期待与您一起创造美好未来！

© {self.default_vars["current_year"]} {self.default_vars["service_name"]}. All rights reserved.
        """

        return html_content, text_content.strip()


# 创建全局实例
email_template_manager = EmailTemplateManager()
