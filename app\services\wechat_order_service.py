"""
微信支付订单数据库操作服务
"""

import logging
from datetime import datetime
from typing import Optional

from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.payment import PaymentRecord, PaymentType
from app.models.wechat_order import (
    WeChatOrder,
    WeChatRefund,
    WeChatTradeState,
)
from app.schemas.wechat_order import (
    WeChatOrderCreate,
    WeChatOrderUpdate,
    WeChatRefundCreate,
)

logger = logging.getLogger(__name__)


class WeChatOrderService:
    """微信支付订单数据库操作服务"""

    @staticmethod
    async def create_order(
        db: AsyncSession,
        user_id: int,
        order_data: WeChatOrderCreate,
        out_trade_no: str,
        prepay_id: Optional[str] = None,
        code_url: Optional[str] = None,
        h5_url: Optional[str] = None,
    ) -> WeChatOrder:
        """创建微信支付订单"""

        # 创建微信支付订单
        wechat_order = WeChatOrder(
            user_id=user_id,
            product_id=order_data.product_id,
            out_trade_no=out_trade_no,
            prepay_id=prepay_id,
            description=order_data.description,
            total=order_data.total,
            trade_type=order_data.trade_type,
            trade_state=WeChatTradeState.NOTPAY,
            code_url=code_url,
            h5_url=h5_url,
            openid=order_data.openid,
        )

        db.add(wechat_order)
        await db.flush()  # 获取order_id

        # 创建对应的支付记录
        payment_record = PaymentRecord(
            user_id=user_id,
            amount=order_data.total / 100,  # 转换为元
            payment_type=PaymentType.PURCHASE,
            product_id=order_data.product_id,
            order_id=out_trade_no,
            payment_method="WeChat",
        )

        db.add(payment_record)
        await db.flush()  # 获取payment_id

        # 关联支付记录
        wechat_order.payment_record_id = payment_record.payment_id

        await db.commit()
        await db.refresh(wechat_order)

        logger.info(f"创建微信支付订单成功: {out_trade_no}, 用户: {user_id}")
        return wechat_order

    @staticmethod
    async def get_order_by_trade_no(
        db: AsyncSession, out_trade_no: str, user_id: Optional[int] = None
    ) -> Optional[WeChatOrder]:
        """根据商户订单号获取订单"""

        query = select(WeChatOrder).where(WeChatOrder.out_trade_no == out_trade_no)

        if user_id is not None:
            query = query.where(WeChatOrder.user_id == user_id)

        result = await db.execute(query)
        return result.scalar_one_or_none()

    @staticmethod
    async def get_order_by_id(
        db: AsyncSession, order_id: int, user_id: Optional[int] = None
    ) -> Optional[WeChatOrder]:
        """根据订单ID获取订单"""

        query = select(WeChatOrder).where(WeChatOrder.order_id == order_id)

        if user_id is not None:
            query = query.where(WeChatOrder.user_id == user_id)

        result = await db.execute(query)
        return result.scalar_one_or_none()

    @staticmethod
    async def update_order(
        db: AsyncSession, out_trade_no: str, update_data: WeChatOrderUpdate
    ) -> Optional[WeChatOrder]:
        """更新订单信息"""

        order = await WeChatOrderService.get_order_by_trade_no(db, out_trade_no)
        if not order:
            return None

        # 更新字段
        for field, value in update_data.model_dump(exclude_unset=True).items():
            setattr(order, field, value)

        order.updated_at = datetime.utcnow()

        await db.commit()
        await db.refresh(order)

        logger.info(f"更新微信支付订单: {out_trade_no}")
        return order

    @staticmethod
    async def update_payment_success(
        db: AsyncSession,
        out_trade_no: str,
        transaction_id: str,
        success_time: Optional[datetime] = None,
    ) -> Optional[WeChatOrder]:
        """更新支付成功状态"""

        if success_time is None:
            success_time = datetime.utcnow()

        update_data = WeChatOrderUpdate(
            transaction_id=transaction_id,
            trade_state=WeChatTradeState.SUCCESS,
            success_time=success_time,
            notify_received=True,
            notify_time=datetime.utcnow(),
        )

        order = await WeChatOrderService.update_order(db, out_trade_no, update_data)

        if order:
            logger.info(f"微信支付成功: {out_trade_no}, 微信订单号: {transaction_id}")

        return order

    @staticmethod
    async def get_user_orders(
        db: AsyncSession, user_id: int, limit: int = 20, offset: int = 0
    ) -> list[WeChatOrder]:
        """获取用户的订单列表"""

        query = (
            select(WeChatOrder)
            .where(WeChatOrder.user_id == user_id)
            .order_by(desc(WeChatOrder.created_at))
            .limit(limit)
            .offset(offset)
        )

        result = await db.execute(query)
        return list(result.scalars().all())

    @staticmethod
    async def count_user_orders(db: AsyncSession, user_id: int) -> int:
        """统计用户订单总数"""
        query = select(func.count(WeChatOrder.order_id)).where(
            WeChatOrder.user_id == user_id
        )
        result = await db.execute(query)
        return result.scalar() or 0

    @staticmethod
    async def get_pending_orders(
        db: AsyncSession, limit: int = 100
    ) -> list[WeChatOrder]:
        """获取待支付的订单"""

        query = (
            select(WeChatOrder)
            .where(WeChatOrder.trade_state == WeChatTradeState.NOTPAY)
            .order_by(desc(WeChatOrder.created_at))
            .limit(limit)
        )

        result = await db.execute(query)
        return list(result.scalars().all())

    @staticmethod
    async def create_refund(
        db: AsyncSession, wechat_order_id: int, refund_data: WeChatRefundCreate
    ) -> WeChatRefund:
        """创建退款记录"""

        refund = WeChatRefund(
            wechat_order_id=wechat_order_id,
            out_refund_no=refund_data.out_refund_no,
            refund_fee=refund_data.refund_fee,
            total_fee=refund_data.total_fee,
            reason=refund_data.reason,
        )

        db.add(refund)
        await db.commit()
        await db.refresh(refund)

        logger.info(f"创建退款记录: {refund_data.out_refund_no}")
        return refund

    @staticmethod
    async def get_refund_by_refund_no(
        db: AsyncSession, out_refund_no: str
    ) -> Optional[WeChatRefund]:
        """根据退款单号获取退款记录"""

        query = select(WeChatRefund).where(WeChatRefund.out_refund_no == out_refund_no)
        result = await db.execute(query)
        return result.scalar_one_or_none()

    @staticmethod
    async def update_refund(
        db: AsyncSession, out_refund_no: str, update_data
    ) -> Optional[WeChatRefund]:
        """更新退款记录"""

        refund = await WeChatOrderService.get_refund_by_refund_no(db, out_refund_no)
        if not refund:
            return None

        # 更新字段
        for field, value in update_data.model_dump(exclude_unset=True).items():
            setattr(refund, field, value)

        await db.commit()
        await db.refresh(refund)

        logger.info(f"更新退款记录: {out_refund_no}")
        return refund

    @staticmethod
    async def get_order_stats(
        db: AsyncSession,
        user_id: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
    ) -> dict:
        """获取订单统计信息"""

        # 基础查询条件
        conditions = []

        if user_id:
            conditions.append(WeChatOrder.user_id == user_id)

        if start_date:
            conditions.append(WeChatOrder.created_at >= start_date)

        if end_date:
            conditions.append(WeChatOrder.created_at <= end_date)

        where_clause = and_(*conditions) if conditions else True

        # 统计查询
        stats_query = select(
            func.count(WeChatOrder.order_id).label("total_orders"),
            func.count(
                func.case((WeChatOrder.trade_state == WeChatTradeState.SUCCESS, 1))
            ).label("success_orders"),
            func.count(
                func.case((WeChatOrder.trade_state == WeChatTradeState.NOTPAY, 1))
            ).label("pending_orders"),
            func.sum(
                func.case(
                    (
                        WeChatOrder.trade_state == WeChatTradeState.SUCCESS,
                        WeChatOrder.total,
                    ),
                    else_=0,
                )
            ).label("success_amount"),
            func.sum(WeChatOrder.total).label("total_amount"),
        ).where(where_clause)

        result = await db.execute(stats_query)
        stats = result.first()

        # 计算成功率
        success_rate = 0.0
        if stats.total_orders > 0:
            success_rate = (stats.success_orders / stats.total_orders) * 100

        return {
            "total_orders": stats.total_orders or 0,
            "success_orders": stats.success_orders or 0,
            "pending_orders": stats.pending_orders or 0,
            "failed_orders": (stats.total_orders or 0)
            - (stats.success_orders or 0)
            - (stats.pending_orders or 0),
            "total_amount": (stats.total_amount or 0) / 100,  # 转换为元
            "success_amount": (stats.success_amount or 0) / 100,  # 转换为元
            "success_rate": round(success_rate, 2),
        }

    @staticmethod
    async def cleanup_expired_orders(db: AsyncSession, expire_hours: int = 24) -> int:
        """清理过期的未支付订单"""

        from datetime import timedelta

        expire_time = datetime.utcnow() - timedelta(hours=expire_hours)

        # 查找过期的未支付订单
        query = select(WeChatOrder).where(
            and_(
                WeChatOrder.trade_state == WeChatTradeState.NOTPAY,
                WeChatOrder.created_at < expire_time,
            )
        )

        result = await db.execute(query)
        expired_orders = result.scalars().all()

        # 更新状态为已关闭
        count = 0
        for order in expired_orders:
            order.trade_state = WeChatTradeState.CLOSED
            order.updated_at = datetime.utcnow()
            count += 1

        if count > 0:
            await db.commit()
            logger.info(f"清理过期订单: {count} 个")

        return count
