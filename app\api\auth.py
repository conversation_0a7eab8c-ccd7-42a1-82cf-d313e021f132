"""
认证相关API路由
"""

from datetime import timedelta

from fastapi import APIRouter, Depends, Form, HTTPException, Request, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.datetime_utils import default_datetime
from app.core.email_verification import (
    create_verification_code,
    resend_verification_code,
    verify_email_code,
)
from app.core.security import (
    authenticate_user,
    create_access_token,
    get_current_active_user,
    get_password_hash,
)
from app.db.database import get_db
from app.models.user import User, UserStatus
from app.schemas.common import AuthResponse, Token
from app.schemas.user import UserLogin, UserResponse

router = APIRouter()


async def _check_email_exists_with_dedicated_connection(email: str) -> bool:
    """使用纯asyncpg连接检查邮箱是否已存在，完全避免SQLAlchemy"""
    import asyncpg
    from app.core.config import settings

    # 获取数据库URL
    database_url = settings.get_effective_database_url()

    # 直接使用asyncpg，完全绕过SQLAlchemy
    try:
        # 解析数据库URL
        if database_url.startswith('postgresql+asyncpg://'):
            database_url = database_url.replace('postgresql+asyncpg://', 'postgresql://')
        elif database_url.startswith('postgres://'):
            database_url = database_url.replace('postgres://', 'postgresql://')

        # 创建直接的asyncpg连接，禁用prepared statements
        conn = await asyncpg.connect(database_url, statement_cache_size=0)

        try:
            # 只检查已激活的用户，允许PENDING状态的用户重新发送验证码
            result = await conn.fetchval("SELECT user_id FROM users WHERE email = $1 AND status = 'ACTIVE' LIMIT 1", email)
            return result is not None
        finally:
            await conn.close()

    except Exception as e:
        print(f"邮箱检查失败: {str(e)}")
        return False



async def _create_user_and_send_verification_native(email: str) -> dict:
    """使用完全原生SQL创建用户并发送验证码，完全避免SQLAlchemy会话"""
    import asyncpg
    import random
    import string
    from datetime import datetime, timedelta
    from app.core.config import settings
    from app.core.security import get_password_hash

    # 生成6位验证码
    verification_code = ''.join(random.choices(string.digits, k=6))

    # 生成临时密码
    temp_password = get_password_hash("temp_password_for_verification")

    # 验证码过期时间（15分钟）
    expires_at = datetime.utcnow() + timedelta(minutes=15)

    # 获取数据库URL
    database_url = settings.get_effective_database_url()

    # 直接使用asyncpg，完全绕过SQLAlchemy
    try:
        # 解析数据库URL
        if database_url.startswith('postgresql+asyncpg://'):
            database_url = database_url.replace('postgresql+asyncpg://', 'postgresql://')
        elif database_url.startswith('postgres://'):
            database_url = database_url.replace('postgres://', 'postgresql://')

        # 创建直接的asyncpg连接，禁用prepared statements
        conn = await asyncpg.connect(database_url, statement_cache_size=0)

        try:
            # 检查是否已有PENDING状态的用户，如果有则更新验证码，否则创建新用户
            existing_user = await conn.fetchval("""
                SELECT user_id FROM users WHERE email = $1 AND status = 'PENDING'
            """, email)

            if existing_user:
                # 更新现有PENDING用户的验证码
                user_id = await conn.fetchval("""
                    UPDATE users
                    SET email_verification_code = $2,
                        email_verification_expires = $3
                    WHERE email = $1 AND status = 'PENDING'
                    RETURNING user_id
                """, email, verification_code, expires_at)
            else:
                # 创建新的临时用户记录
                user_id = await conn.fetchval("""
                    INSERT INTO users (email, password_hash, status, role, register_date, email_verified,
                                     email_verification_code, email_verification_expires)
                    VALUES ($1, $2, 'PENDING', 'USER', $3, false, $4, $5)
                    RETURNING user_id
                """, email, temp_password, datetime.utcnow(), verification_code, expires_at)

            # 使用延迟导入发送验证码邮件，避免模块加载时的SQLAlchemy冲突
            email_success = False
            try:
                # 延迟导入邮件服务，避免在模块加载时触发SQLAlchemy引擎
                import importlib
                email_service_module = importlib.import_module('app.core.email_service')
                email_service = email_service_module.email_service

                # 异步发送验证码邮件
                email_success = await email_service.send_verification_email(email, verification_code)

                if email_success:
                    print(f"✅ 验证码邮件已发送到: {email}")
                else:
                    print(f"❌ 验证码邮件发送失败: {email}")

            except Exception as e:
                print(f"❌ 邮件发送异常: {str(e)}")
                email_success = False

            # 如果邮件发送失败，显示验证码到终端作为备用
            if not email_success:
                print("\n" + "=" * 60)
                print("🔥🔥🔥 邮件发送失败，验证码备用显示 🔥🔥🔥")
                print("=" * 60)
                print(f"📧 收件人: {email}")
                print(f"🔢 验证码: {verification_code}")
                print("⏰ 有效期: 15分钟")
                print("=" * 60)
                print("请使用此验证码完成注册")
                print("=" * 60 + "\n")

            return {
                "user_id": user_id,
                "code": verification_code,
                "expires_at": expires_at
            }

        finally:
            await conn.close()

    except Exception as e:
        raise Exception(f"Failed to create user with native SQL: {str(e)}")


async def _verify_email_code_native(email: str, verification_code: str) -> bool:
    """使用原生SQL验证邮箱验证码，避免SQLAlchemy会话冲突"""
    import asyncpg
    from datetime import datetime
    from app.core.config import settings

    # 获取数据库URL
    database_url = settings.get_effective_database_url()

    # 直接使用asyncpg，完全绕过SQLAlchemy
    try:
        # 解析数据库URL
        if database_url.startswith('postgresql+asyncpg://'):
            database_url = database_url.replace('postgresql+asyncpg://', 'postgresql://')
        elif database_url.startswith('postgres://'):
            database_url = database_url.replace('postgres://', 'postgresql://')

        # 创建直接的asyncpg连接，禁用prepared statements
        conn = await asyncpg.connect(database_url, statement_cache_size=0)

        try:
            # 查询验证码是否有效
            result = await conn.fetchrow("""
                SELECT user_id, email_verification_expires
                FROM users
                WHERE email = $1 AND email_verification_code = $2 AND status = 'PENDING'
            """, email, verification_code)

            if not result:
                return False

            # 检查验证码是否过期
            if result['email_verification_expires'] and result['email_verification_expires'] < datetime.utcnow():
                return False

            return True

        finally:
            await conn.close()

    except Exception as e:
        print(f"验证码验证失败: {str(e)}")
        return False


async def _activate_user_native(email: str, password_hash: str, nickname: str = None) -> dict:
    """使用原生SQL激活用户，避免SQLAlchemy会话冲突"""
    import asyncpg
    from datetime import datetime
    from app.core.config import settings

    # 获取数据库URL
    database_url = settings.get_effective_database_url()

    # 直接使用asyncpg，完全绕过SQLAlchemy
    try:
        # 解析数据库URL
        if database_url.startswith('postgresql+asyncpg://'):
            database_url = database_url.replace('postgresql+asyncpg://', 'postgresql://')
        elif database_url.startswith('postgres://'):
            database_url = database_url.replace('postgres://', 'postgresql://')

        # 创建直接的asyncpg连接，禁用prepared statements
        conn = await asyncpg.connect(database_url, statement_cache_size=0)

        try:
            # 更新用户状态为激活，并设置最终密码
            result = await conn.fetchrow("""
                UPDATE users
                SET status = 'ACTIVE',
                    password_hash = $2,
                    nickname = $3,
                    email_verified = true,
                    email_verification_code = NULL,
                    email_verification_expires = NULL
                WHERE email = $1 AND status = 'PENDING'
                RETURNING user_id, email, status, role, register_date, email_verified
            """, email, password_hash, nickname)

            if result:
                return {
                    "user_id": result['user_id'],
                    "email": result['email'],
                    "status": result['status'],
                    "role": result['role'],
                    "register_date": result['register_date'],
                    "email_verified": result['email_verified']
                }
            else:
                raise Exception("Failed to activate user")

        finally:
            await conn.close()

    except Exception as e:
        raise Exception(f"Failed to activate user with native SQL: {str(e)}")


def check_rate_limit(request: Request, limit: int, window: int, action: str) -> None:
    """检查速率限制"""
    from app.core.security import rate_limiter

    client_ip = (
        getattr(request.client, "host", "127.0.0.1") if request.client else "127.0.0.1"
    )
    key = f"{client_ip}:{action}"

    if not rate_limiter.is_allowed(key, limit, window):
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=f"Rate limit exceeded for {action}. Please try again later.",
        )


def validate_password_strength(password: str) -> None:
    """验证密码长度"""
    if len(password) < settings.MIN_PASSWORD_LENGTH:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Password must be at least {settings.MIN_PASSWORD_LENGTH} characters long",
        )


@router.post(
    "/register",
    operation_id="auth_register",
    include_in_schema=not settings.ADMIN_ONLY_MODE,
)
async def register(
    email: str = Form(...),
    password: str = Form(...),
    verification_code: str = Form(...),
    nickname: str = Form(None),
    request: Request = None,
    db: AsyncSession = Depends(get_db),
):
    """用户注册"""
    # 检查速率限制
    check_rate_limit(
        request,
        settings.REGISTRATION_RATE_LIMIT,
        settings.AUTH_RATE_LIMIT_WINDOW,
        "register",
    )

    # 检查是否启用用户注册
    if not settings.allow_user_registration:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User registration is disabled, please contact administrator for account",
        )

    try:
        # 验证密码强度
        validate_password_strength(password)

        # 如果需要邮箱验证，验证用户提供的验证码
        if settings.REQUIRE_EMAIL_VERIFICATION and verification_code:
            # 使用原生SQL验证邮箱验证码，避免SQLAlchemy会话冲突
            is_valid = await _verify_email_code_native(email, verification_code)

            if not is_valid:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid or expired verification code",
                )

            # 验证成功，使用原生SQL激活用户
            user_data = await _activate_user_native(email, get_password_hash(password), nickname)

            # 创建用户对象用于返回token
            from app.models.user import User, UserStatus, UserRole
            new_user = User(
                user_id=user_data["user_id"],
                email=user_data["email"],
                status=UserStatus.ACTIVE,
                role=UserRole.USER,
                register_date=user_data["register_date"],
                email_verified=user_data["email_verified"]
            )
        else:
            # 如果不需要验证码，说明流程有问题
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Verification code is required for registration",
            )

        # 如果需要邮箱验证但没有提供验证码，返回错误
        if settings.REQUIRE_EMAIL_VERIFICATION and not verification_code:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Verification code is required. Please use /api/auth/send-register-code first.",
            )

        # 如果不需要邮箱验证，或者验证码验证成功，直接返回令牌
        if not settings.REQUIRE_EMAIL_VERIFICATION or (
            settings.REQUIRE_EMAIL_VERIFICATION and verification_code
        ):
            access_token_expires = timedelta(
                minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
            )
            access_token = create_access_token(
                data={"sub": new_user.email, "user_id": new_user.user_id},
                expires_delta=access_token_expires,
            )

            token = Token(
                access_token=access_token,
                token_type="bearer",
                expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            )

            user_response = UserResponse.model_validate(new_user)

            return AuthResponse(
                success=True,
                message="Registration successful",
                token=token,
                user=user_response.model_dump(),
            )

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Registration failed: {str(e)}",
        )


@router.post(
    "/login", operation_id="auth_login", include_in_schema=not settings.ADMIN_ONLY_MODE
)
async def login(
    user_credentials: UserLogin, request: Request, db: AsyncSession = Depends(get_db)
):
    """用户登录认证"""
    # 检查速率限制
    check_rate_limit(
        request, settings.LOGIN_RATE_LIMIT, settings.AUTH_RATE_LIMIT_WINDOW, "login"
    )

    # 在管理员模式下，普通用户登录被禁用
    if settings.ADMIN_ONLY_MODE:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User login is disabled, please use admin login endpoint /api/admin/login",
        )

    try:
        # 1. 首先尝试本地用户认证
        user = await authenticate_user(
            db, user_credentials.email, user_credentials.password
        )

        # 2. 如果本地认证失败，尝试原网站认证
        if not user:
            from app.api.legacy import try_legacy_login

            user = await try_legacy_login(
                user_credentials.email, user_credentials.password, db
            )

        # 3. 如果两种认证都失败，返回错误
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 更新最后登录时间
        user.last_login_date = default_datetime()
        await db.commit()

        # 创建访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.email, "user_id": user.user_id},
            expires_delta=access_token_expires,
        )

        token = Token(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        )

        user_response = UserResponse.model_validate(user)

        return AuthResponse(
            success=True,
            message="Login successful",
            token=token,
            user=user_response.model_dump(),
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Login failed: {str(e)}",
        )


@router.post("/token", response_model=Token, operation_id="auth_token")
async def login_for_access_token(
    request: Request,
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_db),
):
    """
    OAuth2兼容的token端点
    支持用户和管理员登录获取访问令牌
    """
    # 检查速率限制 - OAuth2端点也需要保护
    check_rate_limit(
        request,
        settings.TOKEN_RATE_LIMIT,
        settings.AUTH_RATE_LIMIT_WINDOW,
        "oauth2_token",
    )

    try:
        # 首先尝试管理员认证
        from app.core.admin import AdminAuthService

        admin_user = await AdminAuthService.authenticate_admin(
            form_data.username, form_data.password, db
        )

        if admin_user:
            # 管理员认证成功
            access_token_expires = timedelta(
                minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
            )

            if isinstance(admin_user, dict):
                # 超级管理员
                access_token = create_access_token(
                    data={
                        "sub": admin_user["email"],
                        "user_id": admin_user["user_id"],
                        "role": "super_admin",
                    },
                    expires_delta=access_token_expires,
                )
            else:
                # 数据库管理员
                access_token = create_access_token(
                    data={
                        "sub": admin_user.email,
                        "user_id": admin_user.user_id,
                        "role": admin_user.role.value,
                    },
                    expires_delta=access_token_expires,
                )

            return Token(
                access_token=access_token,
                token_type="bearer",
                expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            )

        # 如果不是管理员，尝试普通用户认证（仅在允许的模式下）
        if settings.allow_user_login:
            # 1. 首先尝试本地用户认证
            user = await authenticate_user(db, form_data.username, form_data.password)

            # 2. 如果本地认证失败，尝试原网站认证
            if not user:
                from app.api.legacy import try_legacy_login

                user = await try_legacy_login(
                    form_data.username, form_data.password, db
                )

            if user:
                # 更新最后登录时间
                user.last_login_date = default_datetime()
                await db.commit()

                # 创建访问令牌
                access_token_expires = timedelta(
                    minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
                )
                access_token = create_access_token(
                    data={"sub": user.email, "user_id": user.user_id},
                    expires_delta=access_token_expires,
                )

                return Token(
                    access_token=access_token,
                    token_type="bearer",
                    expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
                )

        # 认证失败
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Authentication failed: {str(e)}",
        )


@router.post("/verify-email", operation_id="auth_verify_email")
async def verify_email(
    email: str,
    verification_code: str,
    request: Request,
    db: AsyncSession = Depends(get_db),
):
    """验证邮箱验证码"""
    # 检查速率限制 - 防止验证码暴力破解
    check_rate_limit(
        request,
        settings.VERIFY_EMAIL_RATE_LIMIT,
        settings.AUTH_RATE_LIMIT_WINDOW,
        "verify_email",
    )

    try:
        # 验证邮箱验证码
        is_valid = await verify_email_code(db, email, verification_code)

        if not is_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired verification code",
            )

        # 验证成功，获取用户信息并生成令牌
        result = await db.execute(select(User).where(User.email == email))
        user = result.scalar_one_or_none()

        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )

        # 创建访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.email, "user_id": user.user_id},
            expires_delta=access_token_expires,
        )

        token = Token(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        )

        user_response = UserResponse.model_validate(user)

        return AuthResponse(
            success=True,
            message="Email verification successful",
            token=token,
            user=user_response.model_dump(),
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Email verification failed: {str(e)}",
        )


@router.post("/send-register-code", operation_id="auth_send_register_code")
async def send_register_code(
    email: str = Form(...), request: Request = None
):
    """发送注册验证码 - 使用原生SQL，完全避免SQLAlchemy会话"""
    # 检查速率限制
    check_rate_limit(
        request,
        settings.RESEND_VERIFICATION_RATE_LIMIT,
        settings.AUTH_RATE_LIMIT_WINDOW,
        "send_register_code",
    )

    try:
        # 使用专用连接检查邮箱是否已存在，避免并发冲突
        existing_user = await _check_email_exists_with_dedicated_connection(email)

        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered",
            )

        # 使用完全原生SQL方案创建用户和发送验证码，避免SQLAlchemy会话
        verification_result = await _create_user_and_send_verification_native(email)

        return AuthResponse(
            success=True,
            message=f"Verification code sent to {email}",
            user={"email": email, "verification_required": True, "verification_code": verification_result["code"]},
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to prepare registration: {str(e)}",
        )


@router.post("/resend-verification", operation_id="auth_resend_verification")
async def resend_verification(
    email: str, request: Request, db: AsyncSession = Depends(get_db)
):
    """重新发送验证码"""
    # 检查速率限制
    check_rate_limit(
        request,
        settings.RESEND_VERIFICATION_RATE_LIMIT,
        settings.AUTH_RATE_LIMIT_WINDOW,
        "resend_verification",
    )

    try:
        verification_code = await resend_verification_code(db, email)

        # 开发环境下提供详细信息，生产环境统一响应
        if settings.IS_DEVELOPMENT:
            if verification_code is None:
                return AuthResponse(
                    success=False,
                    message=f"[DEV] User not found or email already verified: {email}",
                    user={"email": email},
                )
            else:
                return AuthResponse(
                    success=True,
                    message=f"[DEV] Verification code resent to {email}",
                    user={"email": email},
                )
        else:
            # 生产环境统一响应，防止账户枚举攻击
            return AuthResponse(
                success=True,
                message=f"If the email exists and is not verified, a verification code has been sent to {email}",
                user={"email": email},
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to resend verification code: {str(e)}",
        )


@router.get(
    "/me",
    response_model=UserResponse,
    operation_id="auth_me",
    include_in_schema=not settings.ADMIN_ONLY_MODE,
)
async def get_current_user_info(current_user: User = Depends(get_current_active_user)):
    """获取当前用户信息"""
    return UserResponse.model_validate(current_user)
