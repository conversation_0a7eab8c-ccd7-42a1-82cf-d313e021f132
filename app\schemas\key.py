"""
秘钥相关Pydantic模式定义
"""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict, Field

from app.schemas.common import KeyStatus


# 秘钥相关模式
class KeyBase(BaseModel):
    """秘钥基础模式"""

    product_id: Optional[int] = None
    price: float = Field(..., ge=0)
    start_date: datetime
    end_date: datetime
    max_activation_count: int = Field(..., ge=1)


class KeyCreate(KeyBase):
    """秘钥创建模式"""

    pass


class KeyUpdate(BaseModel):
    """秘钥更新模式"""

    price: Optional[float] = Field(None, ge=0)
    end_date: Optional[datetime] = None
    max_activation_count: Optional[int] = Field(None, ge=1)
    status: Optional[KeyStatus] = None


class KeyResponse(KeyBase):
    """秘钥响应模式"""

    model_config = ConfigDict(from_attributes=True)

    key_id: int
    user_id: int
    key_string: str
    activated_count: int
    status: KeyStatus
    created_date: datetime
    updated_date: datetime


# 秘钥激活相关模式
class KeyActivationBase(BaseModel):
    """秘钥激活基础模式"""

    device_id: Optional[str] = Field(None, max_length=255)
    ip_address: Optional[str] = Field(None, max_length=45)


class KeyActivationCreate(KeyActivationBase):
    """秘钥激活创建模式"""

    key_id: int


class KeyActivationResponse(KeyActivationBase):
    """秘钥激活响应模式"""

    model_config = ConfigDict(from_attributes=True)

    activation_id: int
    key_id: int
    user_id: int
    activation_date: datetime
