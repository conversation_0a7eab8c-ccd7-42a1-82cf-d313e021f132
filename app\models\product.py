"""
产品相关数据模型
"""

from datetime import datetime
from typing import TYPE_CHECKING, Optional

from sqlalchemy import DECIMAL, DateTime, Integer, String, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.core.datetime_utils import default_datetime
from app.db.database import Base

if TYPE_CHECKING:
    from .key import Key
    from .payment import PaymentRecord
    from .wechat_order import WeChatOrder


class Product(Base):
    """产品表 - 根据数据库设计规范"""

    __tablename__ = "products"

    product_id: Mapped[int] = mapped_column(
        Integer, primary_key=True, autoincrement=True
    )
    product_name: Mapped[str] = mapped_column(String(100), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    default_price: Mapped[Optional[float]] = mapped_column(DECIMAL(10, 2))
    created_date: Mapped[datetime] = mapped_column(DateTime, default=default_datetime)

    # 关系
    keys: Mapped[list["Key"]] = relationship("Key", back_populates="product")
    payment_records: Mapped[list["PaymentRecord"]] = relationship(
        "PaymentRecord", back_populates="product"
    )
    wechat_orders: Mapped[list["WeChatOrder"]] = relationship(
        "WeChatOrder", back_populates="product"
    )
