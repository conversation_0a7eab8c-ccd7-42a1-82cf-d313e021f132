"""
用户相关API路由
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.security import get_current_active_user
from app.db.database import get_db
from app.models.key import Key
from app.models.user import User
from app.schemas.key import KeyResponse
from app.schemas.user import UserResponse

router = APIRouter()


@router.get("/profile", response_model=UserResponse, operation_id="users_profile")
async def get_user_profile(current_user: User = Depends(get_current_active_user)):
    """获取当前用户信息"""
    return UserResponse.model_validate(current_user)


@router.get("/keys", response_model=list[KeyResponse], operation_id="users_keys")
async def get_user_keys(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """获取当前用户的所有秘钥"""
    try:
        result = await db.execute(
            select(Key).where(Key.user_id == current_user.user_id)
        )
        keys = result.scalars().all()
        return [KeyResponse.model_validate(key) for key in keys]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get user keys: {str(e)}",
        )
