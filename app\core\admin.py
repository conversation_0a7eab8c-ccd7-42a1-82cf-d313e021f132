"""
管理员认证和权限管理模块
支持混合模式：环境变量超级管理员 + 数据库管理员
"""

import logging
from typing import Optional, Union

from fastapi import Depends, HTTPException, status
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.security import (
    get_current_user_from_token,
    get_password_hash,
    verify_password,
)
from app.models.user import User, UserRole, UserStatus

logger = logging.getLogger(__name__)


class AdminAuthService:
    """管理员认证服务"""

    @staticmethod
    def is_super_admin_credentials(email: str, password: str) -> bool:
        """检查是否为超级管理员凭据（环境变量配置）"""
        return (
            email == settings.SUPER_ADMIN_EMAIL
            and password == settings.SUPER_ADMIN_PASSWORD
        )

    @staticmethod
    async def authenticate_admin(
        email: str, password: str, db: AsyncSession
    ) -> Optional[Union[User, dict]]:
        """
        管理员认证
        返回：User对象（数据库管理员）或 dict（超级管理员）
        """
        # 1. 检查超级管理员（环境变量）- 优先级最高
        if AdminAuthService.is_super_admin_credentials(email, password):
            logger.info(f"🔑 环境变量超级管理员登录: {email}")
            return {
                "user_id": 0,  # 特殊ID表示超级管理员
                "email": settings.SUPER_ADMIN_EMAIL,
                "role": UserRole.SUPER_ADMIN,
                "status": UserStatus.ACTIVE,
                "is_super_admin": True,
            }

        # 2. 检查数据库管理员
        try:
            result = await db.execute(select(User).where(User.email == email))
            user = result.scalar_one_or_none()

            if user and verify_password(password, user.password_hash):
                # 检查是否为管理员角色
                if user.role in [UserRole.ADMIN, UserRole.SUPER_ADMIN]:
                    if user.status == UserStatus.ACTIVE:
                        # 特殊标识：如果是环境变量管理员的邮箱，说明是数据库副本
                        if email == settings.SUPER_ADMIN_EMAIL:
                            logger.info(
                                f"🗄️ 数据库管理员登录 (环境变量邮箱副本): {email}, 角色: {user.role}"
                            )
                        else:
                            logger.info(
                                f"🗄️ 数据库管理员登录: {email}, 角色: {user.role}"
                            )
                        return user
                    else:
                        logger.warning(
                            f"管理员账户状态异常: {email}, 状态: {user.status}"
                        )
                        return None
                else:
                    logger.warning(
                        f"用户尝试以管理员身份登录: {email}, 角色: {user.role}"
                    )
                    return None

            return None

        except Exception as e:
            logger.error(f"管理员认证数据库查询失败: {e}")
            return None

    @staticmethod
    async def create_admin_user(
        email: str,
        password: str,
        role: UserRole = UserRole.ADMIN,
        db: Optional[AsyncSession] = None,
    ) -> User:
        """创建管理员用户"""
        if not db:
            raise ValueError("数据库会话不能为空")

        # 检查邮箱是否已存在（允许环境变量管理员邮箱）
        result = await db.execute(select(User).where(User.email == email))
        existing_user = result.scalar_one_or_none()

        if existing_user:
            # 检查是否为环境变量超级管理员的邮箱
            if email == settings.SUPER_ADMIN_EMAIL:
                logger.info(f"环境变量超级管理员邮箱已存在数据库中: {email}")
                logger.warning(
                    f"注意：邮箱 {email} 同时存在环境变量和数据库管理员，登录时将优先使用环境变量认证"
                )

                # 更新现有用户为管理员角色（如果不是的话）
                if existing_user.role not in [UserRole.ADMIN, UserRole.SUPER_ADMIN]:
                    existing_user.role = role
                    existing_user.status = UserStatus.ACTIVE
                    existing_user.email_verified = True
                    await db.commit()
                    await db.refresh(existing_user)
                    logger.info(f"已将现有用户 {email} 升级为管理员，角色: {role}")
                else:
                    logger.info(
                        f"用户 {email} 已经是管理员，角色: {existing_user.role}"
                    )

                return existing_user
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, detail="邮箱已存在"
                )

        # 创建新的管理员用户
        hashed_password = get_password_hash(password)
        admin_user = User(
            email=email,
            password_hash=hashed_password,
            role=role,
            status=UserStatus.ACTIVE,
            email_verified=True,  # 管理员默认已验证
        )

        db.add(admin_user)
        await db.commit()
        await db.refresh(admin_user)

        logger.info(f"创建管理员用户: {email}, 角色: {role}")
        return admin_user


def check_admin_permission(required_role: UserRole = UserRole.ADMIN):
    """
    管理员权限检查装饰器
    """

    async def permission_checker(current_user=Depends(get_current_user_from_token)):
        # 检查是否为超级管理员（环境变量）
        if isinstance(current_user, dict) and current_user.get("is_super_admin"):
            return current_user

        # 检查数据库用户权限
        if not hasattr(current_user, "role"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="权限不足：需要管理员权限"
            )

        user_role = current_user.role

        # 权限级别检查
        role_hierarchy = {UserRole.USER: 0, UserRole.ADMIN: 1, UserRole.SUPER_ADMIN: 2}

        required_level = role_hierarchy.get(required_role, 0)
        user_level = role_hierarchy.get(user_role, 0)

        if user_level < required_level:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"权限不足：需要 {required_role.value} 或更高权限",
            )

        # 检查用户状态
        if hasattr(current_user, "status") and current_user.status != UserStatus.ACTIVE:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="账户状态异常，无法访问管理功能",
            )

        return current_user

    return permission_checker


# 便捷的权限检查函数
def require_admin():
    """要求管理员权限"""
    return check_admin_permission(UserRole.ADMIN)


def require_super_admin():
    """要求超级管理员权限"""
    return check_admin_permission(UserRole.SUPER_ADMIN)


async def get_current_admin(current_user=Depends(get_current_user_from_token)):
    """获取当前管理员用户"""
    # 检查是否为超级管理员（环境变量）
    if isinstance(current_user, dict) and current_user.get("is_super_admin"):
        return current_user

    # 检查数据库用户权限
    if not hasattr(current_user, "role") or current_user.role not in [
        UserRole.ADMIN,
        UserRole.SUPER_ADMIN,
    ]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="权限不足：需要管理员权限"
        )

    if current_user.status != UserStatus.ACTIVE:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="账户状态异常，无法访问管理功能",
        )

    return current_user


def get_admin_info(admin_user: Union[User, dict]) -> dict:
    """获取管理员信息（统一格式）"""
    if isinstance(admin_user, dict):
        # 超级管理员（环境变量）
        return {
            "user_id": admin_user["user_id"],
            "email": admin_user["email"],
            "role": admin_user["role"].value,
            "status": admin_user["status"].value,
            "is_super_admin": True,
            "source": "environment",
        }
    else:
        # 数据库管理员
        return {
            "user_id": admin_user.user_id,
            "email": admin_user.email,
            "role": admin_user.role.value,
            "status": admin_user.status.value,
            "is_super_admin": admin_user.role == UserRole.SUPER_ADMIN,
            "source": "database",
            "register_date": admin_user.register_date.isoformat()
            if admin_user.register_date
            else None,
            "last_login_date": admin_user.last_login_date.isoformat()
            if admin_user.last_login_date
            else None,
        }


class AdminPermissions:
    """管理员权限常量"""

    # 用户管理
    USER_VIEW = "user:view"
    USER_CREATE = "user:create"
    USER_UPDATE = "user:update"
    USER_DELETE = "user:delete"
    USER_BAN = "user:ban"

    # 产品管理
    PRODUCT_VIEW = "product:view"
    PRODUCT_CREATE = "product:create"
    PRODUCT_UPDATE = "product:update"
    PRODUCT_DELETE = "product:delete"

    # 密钥管理
    KEY_VIEW = "key:view"
    KEY_CREATE = "key:create"
    KEY_UPDATE = "key:update"
    KEY_DELETE = "key:delete"
    KEY_ACTIVATE = "key:activate"

    # 系统管理
    SYSTEM_CONFIG = "system:config"
    SYSTEM_LOGS = "system:logs"
    SYSTEM_STATS = "system:stats"

    # 管理员管理（仅超级管理员）
    ADMIN_MANAGE = "admin:manage"


def has_permission(admin_user: Union[User, dict], permission: str) -> bool:
    """检查管理员是否有特定权限"""
    # 超级管理员拥有所有权限
    if isinstance(admin_user, dict) and admin_user.get("is_super_admin"):
        return True

    if hasattr(admin_user, "role") and admin_user.role == UserRole.SUPER_ADMIN:
        return True

    # 普通管理员权限检查
    if hasattr(admin_user, "role") and admin_user.role == UserRole.ADMIN:
        # 管理员管理权限仅限超级管理员
        if permission == AdminPermissions.ADMIN_MANAGE:
            return False
        # 其他权限都允许
        return True

    return False
