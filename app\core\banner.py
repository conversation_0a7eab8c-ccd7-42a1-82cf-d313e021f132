"""
启动横幅和系统信息显示模块
"""

import datetime
import os

from app.core.config import settings


def get_ascii_logo():
    """获取ASCII艺术标志"""
    return r"""
  ______        _     __  __      _        _____              _
 |  ____|      | |   |  \/  |    | |      |_   _|            | |
 | |__ __ _ ___| |_  | \  / | ___| |_ __ _  | |_ __ __ _  __| | ___ _ __
 |  __/ _` / __| __| | |\/| |/ _ \ __/ _` | | | '__/ _` |/ _` |/ _ \ '__|
 | | | (_| \__ \ |_  | |  | |  __/ || (_| |_| |_| | (_| | (_| |  __/ |
 |_|  \__,_|___/\__| |_|  |_|\___|\__\__,_|_____|  \__,_|\__,_|\___|_|

                    🚀 软件授权系统 - 现代化API架构 🚀
"""


def print_startup_banner():
    """
    打印启动横幅（仅在非Vercel环境下执行）
    """
    # 在Vercel环境下不打印横幅
    if os.getenv("VERCEL"):
        return

    # 检查数据库连接状态和类型
    db_info = {"status": "未知", "type": "未知", "url_display": "未配置"}
    try:
        from app.db.database import DATABASE_URL, database_type, engine, supabase

        # 确定数据库类型和显示信息
        if DATABASE_URL:
            db_info["type"] = database_type
            # 安全地显示数据库URL（隐藏密码）
            import re

            safe_url = re.sub(r"://([^:]+):([^@]+)@", r"://\1:***@", DATABASE_URL)
            db_info["url_display"] = (
                f"{safe_url[:50]}..." if len(safe_url) > 50 else safe_url
            )

            # 检查连接状态
            if engine is not None:
                db_info["status"] = "引擎已创建"
            else:
                db_info["status"] = "引擎创建失败"
        elif settings.SUPABASE_URL:
            db_info["type"] = "Supabase"
            db_info["url_display"] = f"{settings.SUPABASE_URL[:50]}..."
            if supabase is not None:
                db_info["status"] = "Supabase已连接"
            else:
                db_info["status"] = "Supabase连接失败"
        else:
            db_info["status"] = "未配置"
            db_info["url_display"] = "未设置数据库连接"

    except Exception as e:
        db_info["status"] = f"检测失败: {str(e)[:30]}..."

    current_year = datetime.datetime.now().year

    # 动态获取端口配置
    host = os.getenv("HOST", "0.0.0.0")
    port = os.getenv("PORT", "8000")
    server_url = (
        f"http://localhost:{port}" if host == "0.0.0.0" else f"http://{host}:{port}"
    )

    banner = f"""{get_ascii_logo()}
==================================================
🎯 系统信息
--------------------------------------------------
应用名称: {settings.APP_NAME}
应用版本: {settings.APP_VERSION}
调试级别: {settings.DEBUG_LEVEL} ({"生产" if settings.DEBUG_LEVEL == 0 else "基础" if settings.DEBUG_LEVEL == 1 else "详细" if settings.DEBUG_LEVEL == 2 else "完整"})
==================================================
🌐 服务器信息
--------------------------------------------------
服务器地址: {server_url}
API文档: {server_url}/docs
ReDoc文档: {server_url}/redoc
API信息: {server_url}/api/info
==================================================
🗄️ 数据库信息
--------------------------------------------------
数据库类型: {db_info["type"]}
数据库地址: {db_info["url_display"]}
连接状态: {db_info["status"]}
==================================================
📚 API 端点
--------------------------------------------------
✅ 主API: /api/* (稳定版本)
⚠️  Legacy: /auth/*, /user/*, /products/* (兼容)
==================================================
© {current_year} Fast MetaTrader Auth Team. 保留所有权利.
==================================================
"""

    print(banner)


def print_shutdown_message():
    """打印关闭消息"""
    if os.getenv("VERCEL"):
        return

    print("\n" + "=" * 50)
    print("🛑 服务器正在关闭...")
    print("👋 感谢使用 Fast MetaTrader Auth 系统!")
    print("=" * 50)
