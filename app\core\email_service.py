"""
异步邮件发送服务 - 基于成功的test_smtp.py实现模式
"""

import logging
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from typing import Optional

import aiosmtplib

from app.core.config import settings

logger = logging.getLogger(__name__)


class EmailService:
    """异步邮件发送服务"""

    def __init__(self):
        self.smtp_host = settings.SMTP_HOST
        self.smtp_port = settings.SMTP_PORT
        self.smtp_username = settings.SMTP_USERNAME
        self.smtp_password = settings.SMTP_PASSWORD
        self.smtp_use_tls = settings.SMTP_USE_TLS
        self.smtp_use_ssl = settings.SMTP_USE_SSL
        self.email_from = settings.EMAIL_FROM
        self.email_from_name = settings.EMAIL_FROM_NAME

    def _validate_config(self) -> bool:
        """验证邮件配置是否完整"""
        required_fields = [
            self.smtp_host,
            self.smtp_username,
            self.smtp_password,
            self.email_from,
        ]

        if not all(required_fields):
            logger.error("邮件配置不完整，请检查 SMTP 相关环境变量")
            return False

        return True

    async def send_email(
        self,
        to_email: str,
        subject: str,
        html_content: str,
        text_content: Optional[str] = None,
    ) -> bool:
        """
        发送邮件 - 支持多种参数组合自动重试

        Args:
            to_email: 收件人邮箱
            subject: 邮件主题
            html_content: HTML邮件内容
            text_content: 纯文本邮件内容（可选）

        Returns:
            bool: 发送是否成功
        """
        if not self._validate_config():
            return False

        # 创建邮件消息
        message = MIMEMultipart("alternative")
        message["From"] = f"{self.email_from_name} <{self.email_from}>"
        message["To"] = to_email
        message["Subject"] = subject

        # 添加纯文本内容
        if text_content:
            text_part = MIMEText(text_content, "plain", "utf-8")
            message.attach(text_part)

        # 添加HTML内容
        html_part = MIMEText(html_content, "html", "utf-8")
        message.attach(html_part)

        # 定义多种参数组合，按优先级排序
        # 智能重试机制：从最常用的配置开始尝试
        tls_configs = [
            {
                "name": "STARTTLS (推荐)",
                "use_tls": False,
                "start_tls": True,
                "description": "普通连接 + STARTTLS升级，适用于端口587",
            },
            {
                "name": "SSL直连",
                "use_tls": True,
                "start_tls": False,
                "description": "SSL直接连接，适用于端口465",
            },
            {
                "name": "无加密连接",
                "use_tls": False,
                "start_tls": False,
                "description": "无加密连接，适用于内网或测试环境",
            },
        ]

        # 尝试每种配置
        for i, config in enumerate(tls_configs, 1):
            try:
                logger.info(
                    f"尝试配置 {i}/{len(tls_configs)}: {config['name']} - {config['description']}"
                )

                await aiosmtplib.send(
                    message,
                    hostname=self.smtp_host,
                    port=self.smtp_port,
                    username=self.smtp_username,
                    password=self.smtp_password,
                    use_tls=config["use_tls"],
                    start_tls=config["start_tls"],
                )

                logger.info(f"邮件发送成功: {to_email} (使用配置: {config['name']})")
                return True

            except Exception as e:
                error_msg = str(e)
                logger.warning(f"配置 {config['name']} 失败: {error_msg}")

                # 如果是最后一个配置，记录为错误
                if i == len(tls_configs):
                    logger.error(
                        f"所有配置都失败，邮件发送失败 {to_email}: {error_msg}"
                    )
                else:
                    logger.info("尝试下一个配置...")

        return False

    async def send_verification_email(
        self, to_email: str, verification_code: str
    ) -> bool:
        """
        发送邮箱验证码邮件

        Args:
            to_email: 收件人邮箱
            verification_code: 验证码

        Returns:
            bool: 发送是否成功
        """
        subject = f"[{self.email_from_name}] 邮箱验证码"

        # HTML邮件内容
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>邮箱验证</title>
        </head>
        <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f5f5f5;">
            <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 20px;">
                <!-- 头部 -->
                <div style="text-align: center; padding: 20px 0; border-bottom: 2px solid #007bff;">
                    <h1 style="color: #007bff; margin: 0; font-size: 28px;">📧 邮箱验证</h1>
                    <p style="color: #666; margin: 10px 0 0 0; font-size: 16px;">{self.email_from_name}</p>
                </div>

                <!-- 主要内容 -->
                <div style="padding: 30px 20px;">
                    <h2 style="color: #333; margin-bottom: 20px;">尊敬的用户，您好！</h2>

                    <p style="color: #666; font-size: 16px; line-height: 1.6; margin-bottom: 30px;">
                        感谢您注册 {self.email_from_name}。为了确保您的账户安全，请使用以下验证码完成邮箱验证：
                    </p>

                    <!-- 验证码框 -->
                    <div style="text-align: center; margin: 30px 0;">
                        <div style="background: linear-gradient(135deg, #007bff, #0056b3);
                                    color: white;
                                    font-size: 36px;
                                    font-weight: bold;
                                    padding: 20px 40px;
                                    border-radius: 12px;
                                    letter-spacing: 8px;
                                    display: inline-block;
                                    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);">
                            {verification_code}
                        </div>
                    </div>

                    <!-- 说明信息 -->
                    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 30px 0;">
                        <h3 style="color: #495057; margin-top: 0;">📋 验证说明：</h3>
                        <ul style="color: #666; line-height: 1.6; margin: 0; padding-left: 20px;">
                            <li>验证码有效期为 <strong>15分钟</strong></li>
                            <li>请在注册页面输入此验证码</li>
                            <li>验证码区分大小写，请准确输入</li>
                            <li>如果验证码过期，可以申请重新发送</li>
                        </ul>
                    </div>

                    <p style="color: #999; font-size: 14px; line-height: 1.6;">
                        如果您没有注册 {self.email_from_name} 账户，请忽略此邮件。<br>
                        此邮件由系统自动发送，请勿直接回复。
                    </p>
                </div>

                <!-- 底部 -->
                <div style="border-top: 1px solid #eee; padding: 20px; text-align: center;">
                    <p style="color: #999; font-size: 12px; margin: 0;">
                        © 2024 {self.email_from_name}. All rights reserved.<br>
                        如有问题，请联系：{self.email_from}
                    </p>
                </div>
            </div>
        </body>
        </html>
        """

        # 纯文本内容（备用）
        text_content = f"""
        [{self.email_from_name}] 邮箱验证码

        尊敬的用户，您好！

        感谢您注册 {self.email_from_name}。

        您的邮箱验证码是：{verification_code}

        验证码有效期为15分钟，请及时使用。

        如果您没有注册账户，请忽略此邮件。

        此邮件由系统自动发送，请勿直接回复。
        如有问题，请联系：{self.email_from}
        """

        return await self.send_email(to_email, subject, html_content, text_content)

    async def test_connection(self) -> bool:
        """测试邮件服务器连接 - 支持多种参数组合"""
        if not self._validate_config():
            return False

        # 创建测试邮件
        test_message = MIMEText("SMTP连接测试", "plain", "utf-8")
        test_message["Subject"] = "SMTP连接测试"
        test_message["From"] = self.email_from
        test_message["To"] = self.email_from  # 发送给自己进行测试

        # 定义多种参数组合
        # 智能重试机制：从最常用的配置开始尝试
        tls_configs = [
            {
                "name": "STARTTLS (推荐)",
                "use_tls": False,
                "start_tls": True,
                "description": "普通连接 + STARTTLS升级",
            },
            {
                "name": "SSL直连",
                "use_tls": True,
                "start_tls": False,
                "description": "SSL直接连接",
            },
            {
                "name": "无加密连接",
                "use_tls": False,
                "start_tls": False,
                "description": "无加密连接",
            },
        ]

        # 尝试每种配置
        for i, config in enumerate(tls_configs, 1):
            try:
                logger.info(
                    f"测试配置 {i}/{len(tls_configs)}: {config['name']} - {config['description']}"
                )

                await aiosmtplib.send(
                    test_message,
                    hostname=self.smtp_host,
                    port=self.smtp_port,
                    username=self.smtp_username,
                    password=self.smtp_password,
                    use_tls=config["use_tls"],
                    start_tls=config["start_tls"],
                )

                logger.info(f"邮件服务器连接测试成功 (使用配置: {config['name']})")
                return True

            except Exception as e:
                error_msg = str(e)
                logger.warning(f"测试配置 {config['name']} 失败: {error_msg}")

                # 如果是最后一个配置，记录为错误
                if i == len(tls_configs):
                    logger.error(f"所有配置都失败，邮件服务器连接测试失败: {error_msg}")
                else:
                    logger.info("尝试下一个测试配置...")

        return False


# 创建全局邮件服务实例
email_service = EmailService()
