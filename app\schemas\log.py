"""
日志相关Pydantic模式定义
"""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict, Field


# 用户日志相关模式
class UserLogBase(BaseModel):
    """用户日志基础模式"""

    action: str = Field(..., max_length=50)
    details: Optional[dict] = None


class UserLogCreate(UserLogBase):
    """用户日志创建模式"""

    pass


class UserLogResponse(UserLogBase):
    """用户日志响应模式"""

    model_config = ConfigDict(from_attributes=True)

    log_id: int
    user_id: int
    action_date: datetime
