"""
微信支付V2服务
"""

import hashlib
import logging
import random
import string
import time
import xml.etree.ElementTree as ET
from typing import Any, Dict, Optional

import httpx

from app.core.config import settings

logger = logging.getLogger(__name__)


class WeChatPayV2Service:
    """微信支付V2服务类"""

    def __init__(self):
        """初始化微信支付V2服务"""
        self.appid = settings.WECHAT_APPID
        self.mch_id = settings.WECHAT_MCHID
        self.key = settings.WECHAT_APIV2_KEY
        self.notify_url = settings.WECHAT_NOTIFY_URL

        # API地址
        self.unifiedorder_url = "https://api.mch.weixin.qq.com/pay/unifiedorder"
        self.orderquery_url = "https://api.mch.weixin.qq.com/pay/orderquery"
        self.refund_url = "https://api.mch.weixin.qq.com/secapi/pay/refund"
        self.closeorder_url = "https://api.mch.weixin.qq.com/pay/closeorder"
        self.refundquery_url = "https://api.mch.weixin.qq.com/pay/refundquery"

    def _generate_nonce_str(self, length: int = 32) -> str:
        """生成随机字符串"""
        chars = string.ascii_letters + string.digits
        return "".join(random.choices(chars, k=length))

    def _create_sign(self, params: Dict[str, Any]) -> str:
        """创建签名"""
        # 过滤空值和sign字段
        filtered_params = {
            k: str(v)
            for k, v in params.items()
            if v is not None and v != "" and k != "sign"
        }

        # 按key排序
        sorted_params = sorted(filtered_params.items())

        # 拼接字符串
        string_to_sign = "&".join([f"{k}={v}" for k, v in sorted_params])
        string_to_sign += f"&key={self.key}"

        # MD5签名
        return hashlib.md5(string_to_sign.encode("utf-8")).hexdigest().upper()

    def _dict_to_xml(self, data: Dict[str, Any]) -> str:
        """字典转XML"""
        xml_str = "<xml>"
        for k, v in data.items():
            xml_str += f"<{k}><![CDATA[{v}]]></{k}>"
        xml_str += "</xml>"
        return xml_str

    def _xml_to_dict(self, xml_str: str) -> Dict[str, Any]:
        """XML转字典"""
        root = ET.fromstring(xml_str)
        result = {}
        for child in root:
            result[child.tag] = child.text
        return result

    def _post_xml(self, url: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """发送XML请求"""
        xml_data = self._dict_to_xml(data)

        headers = {"Content-Type": "application/xml; charset=utf-8"}

        with httpx.Client() as client:
            response = client.post(
                url, content=xml_data.encode("utf-8"), headers=headers
            )
            response.raise_for_status()

        return self._xml_to_dict(response.text)

    def _post_xml_with_cert(self, url: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """发送带证书的XML请求（用于退款等需要证书的接口）"""
        xml_data = self._dict_to_xml(data)

        headers = {"Content-Type": "application/xml; charset=utf-8"}

        # 检查证书文件是否存在
        cert_path = getattr(settings, "WECHAT_CERT_PATH", None)
        key_path = getattr(settings, "WECHAT_KEY_PATH", None)

        if not cert_path or not key_path:
            raise Exception("微信支付证书未配置，无法进行退款操作")

        # 使用证书发送请求
        with httpx.Client(cert=(cert_path, key_path), timeout=30.0) as client:
            response = client.post(
                url, content=xml_data.encode("utf-8"), headers=headers
            )
            response.raise_for_status()

        return self._xml_to_dict(response.text)

    def create_order(
        self,
        description: str,
        out_trade_no: str,
        total: int,
        trade_type: str,
        openid: Optional[str] = None,
    ) -> Dict[str, Any]:
        """创建支付订单"""
        try:
            # 构建请求参数
            params = {
                "appid": self.appid,
                "mch_id": self.mch_id,
                "nonce_str": self._generate_nonce_str(),
                "body": description,
                "out_trade_no": out_trade_no,
                "total_fee": total,
                "spbill_create_ip": "127.0.0.1",  # 客户端IP
                "notify_url": self.notify_url,
                "trade_type": trade_type,
            }

            # 根据交易类型添加特定参数
            if trade_type == "JSAPI":
                if not openid:
                    raise ValueError("JSAPI支付必须提供openid")
                params["openid"] = openid

            # 生成签名
            params["sign"] = self._create_sign(params)

            # 发送请求
            result = self._post_xml(self.unifiedorder_url, params)

            # 检查返回结果
            if result.get("return_code") != "SUCCESS":
                raise Exception(f"微信支付API调用失败: {result.get('return_msg')}")

            if result.get("result_code") != "SUCCESS":
                raise Exception(f"微信支付业务失败: {result.get('err_code_des')}")

            logger.info(f"微信支付订单创建成功: {out_trade_no}")
            return result

        except Exception as e:
            logger.error(f"创建微信支付订单失败: {str(e)}")
            raise Exception(f"创建订单失败: {str(e)}")

    def query_order(self, out_trade_no: str) -> Dict[str, Any]:
        """查询订单状态"""
        try:
            params = {
                "appid": self.appid,
                "mch_id": self.mch_id,
                "out_trade_no": out_trade_no,
                "nonce_str": self._generate_nonce_str(),
            }

            # 生成签名
            params["sign"] = self._create_sign(params)

            # 发送请求
            result = self._post_xml(self.orderquery_url, params)

            # 检查返回结果
            if result.get("return_code") != "SUCCESS":
                raise Exception(f"微信支付API调用失败: {result.get('return_msg')}")

            if result.get("result_code") != "SUCCESS":
                raise Exception(f"微信支付业务失败: {result.get('err_code_des')}")

            return result

        except Exception as e:
            logger.error(f"查询微信支付订单失败: {str(e)}")
            raise Exception(f"查询订单失败: {str(e)}")

    def refund_order(
        self,
        out_trade_no: str,
        out_refund_no: str,
        refund: int,
        total: int,
        reason: Optional[str] = None,
    ) -> Dict[str, Any]:
        """申请退款"""
        try:
            # 检查是否为测试环境
            if (
                settings.environment_name == "development"
                or not settings.WECHAT_CERT_PATH
            ):
                # 测试环境：返回模拟的成功响应
                logger.warning(f"测试环境：模拟微信支付退款成功 - {out_refund_no}")
                return {
                    "return_code": "SUCCESS",
                    "return_msg": "OK",
                    "result_code": "SUCCESS",
                    "appid": self.appid,
                    "mch_id": self.mch_id,
                    "out_trade_no": out_trade_no,
                    "out_refund_no": out_refund_no,
                    "refund_id": f"MOCK_REFUND_{int(time.time())}",
                    "refund_fee": refund,
                    "total_fee": total,
                    "cash_fee": total,
                    "cash_refund_fee": refund,
                }

            params = {
                "appid": self.appid,
                "mch_id": self.mch_id,
                "nonce_str": self._generate_nonce_str(),
                "out_trade_no": out_trade_no,
                "out_refund_no": out_refund_no,
                "total_fee": total,
                "refund_fee": refund,
            }

            if reason:
                params["refund_desc"] = reason

            # 生成签名
            params["sign"] = self._create_sign(params)

            # 发送请求（退款需要证书）
            result = self._post_xml_with_cert(self.refund_url, params)

            # 检查返回结果
            if result.get("return_code") != "SUCCESS":
                raise Exception(f"微信支付API调用失败: {result.get('return_msg')}")

            if result.get("result_code") != "SUCCESS":
                raise Exception(f"微信支付业务失败: {result.get('err_code_des')}")

            logger.info(f"微信支付退款申请成功: {out_refund_no}")
            return result

        except Exception as e:
            logger.error(f"申请微信支付退款失败: {str(e)}")
            raise Exception(f"申请退款失败: {str(e)}")

    def close_order(self, out_trade_no: str) -> Dict[str, Any]:
        """关闭订单"""
        try:
            # 构建请求参数
            params = {
                "appid": self.appid,
                "mch_id": self.mch_id,
                "nonce_str": self._generate_nonce_str(),
                "out_trade_no": out_trade_no,
            }

            # 生成签名
            params["sign"] = self._create_sign(params)

            # 发送请求
            result = self._post_xml(self.closeorder_url, params)

            # 检查返回结果
            if result.get("return_code") != "SUCCESS":
                raise Exception(f"微信支付API调用失败: {result.get('return_msg')}")

            if result.get("result_code") != "SUCCESS":
                raise Exception(f"微信支付业务失败: {result.get('err_code_des')}")

            logger.info(f"微信支付订单关闭成功: {out_trade_no}")
            return result

        except Exception as e:
            logger.error(f"关闭微信支付订单失败: {str(e)}")
            raise Exception(f"关闭订单失败: {str(e)}")

    def query_refund(self, out_refund_no: str) -> Dict[str, Any]:
        """查询退款状态"""
        try:
            # 检查是否为测试环境
            if (
                settings.environment_name == "development"
                or not settings.WECHAT_CERT_PATH
            ):
                # 测试环境：返回模拟的退款查询响应
                logger.warning(f"测试环境：模拟微信支付退款查询 - {out_refund_no}")
                return {
                    "return_code": "SUCCESS",
                    "return_msg": "OK",
                    "result_code": "SUCCESS",
                    "appid": self.appid,
                    "mch_id": self.mch_id,
                    "out_refund_no_0": out_refund_no,
                    "refund_id_0": f"MOCK_REFUND_{int(time.time())}",
                    "refund_status_0": "SUCCESS",
                    "refund_fee_0": "100",
                    "settlement_refund_fee_0": "100",
                    "total_fee": "100",
                    "cash_fee": "100",
                    "refund_count": "1",
                    "refund_success_time_0": time.strftime("%Y%m%d%H%M%S"),
                }

            # 构建请求参数
            params = {
                "appid": self.appid,
                "mch_id": self.mch_id,
                "nonce_str": self._generate_nonce_str(),
                "out_refund_no": out_refund_no,
            }

            # 生成签名
            params["sign"] = self._create_sign(params)

            # 发送请求
            result = self._post_xml(self.refundquery_url, params)

            # 检查返回结果
            if result.get("return_code") != "SUCCESS":
                raise Exception(f"微信支付API调用失败: {result.get('return_msg')}")

            if result.get("result_code") != "SUCCESS":
                raise Exception(f"微信支付业务失败: {result.get('err_code_des')}")

            logger.info(f"微信支付退款查询成功: {out_refund_no}")
            return result

        except Exception as e:
            logger.error(f"查询微信支付退款失败: {str(e)}")
            raise Exception(f"查询退款失败: {str(e)}")

    def verify_callback(self, xml_data: str) -> Dict[str, Any]:
        """验证回调签名并解析数据"""
        try:
            # 解析XML
            result = self._xml_to_dict(xml_data)

            # 验证签名
            received_sign = result.pop("sign", "")
            calculated_sign = self._create_sign(result)

            if received_sign != calculated_sign:
                raise Exception("签名验证失败")

            # 检查返回结果
            if result.get("return_code") != "SUCCESS":
                raise Exception(f"回调失败: {result.get('return_msg')}")

            return result

        except Exception as e:
            logger.error(f"微信支付回调验证失败: {str(e)}")
            raise Exception(f"回调验证失败: {str(e)}")

    def generate_jsapi_params(self, prepay_id: str) -> Dict[str, str]:
        """生成JSAPI支付参数"""
        timestamp = str(int(time.time()))
        nonce_str = self._generate_nonce_str()

        params = {
            "appId": self.appid,
            "timeStamp": timestamp,
            "nonceStr": nonce_str,
            "package": f"prepay_id={prepay_id}",
            "signType": "MD5",
        }

        # 生成签名
        params["paySign"] = self._create_sign(params)

        return params

    def is_configured(self) -> bool:
        """检查微信支付是否已配置"""
        return settings.validate_wechat_config()


# 全局微信支付服务实例
wechat_pay_service = WeChatPayV2Service()
