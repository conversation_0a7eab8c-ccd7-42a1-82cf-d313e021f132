"""
微信支付V2 API路由 - 测试热重载
"""

import logging
import time
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.security import get_current_active_user
from app.db.database import get_db
from app.models.user import User
from app.schemas.payment import WeChatOrderClose, WeChatRefundQuery
from app.schemas.wechat_order import (
    WeChatJSAPIParams,
    WeChatOrderCreate,
    WeChatOrderQuery,
    WeChatPaymentConfig,
    WeChatPaymentResponse,
    WeChatRefundCreate,
)
from app.services.wechat_order_service import WeChatOrderService
from app.services.wechat_pay_service import wechat_pay_service

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post(
    "/create-order", response_model=WeChatPaymentResponse, summary="创建支付订单"
)
async def create_wechat_order(
    order_data: WeChatOrderCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    创建微信支付订单（需要用户登录）

    **支持的支付方式：**
    - **JSAPI**: 公众号支付，需要用户openid
    - **NATIVE**: 扫码支付，返回二维码链接
    - **MWEB**: H5支付，返回支付链接
    - **APP**: APP支付

    **参数说明：**
    - **description**: 商品描述，会显示在支付页面
    - **total**: 订单金额，单位为分（例如：100 = 1元）
    - **trade_type**: 交易类型
    - **product_id**: 产品ID（可选）
    - **openid**: 用户openid（仅JSAPI支付必填）

    **返回数据：**
    - NATIVE支付：返回code_url，用于生成二维码
    - JSAPI支付：返回prepay_id，用于调起支付
    - H5支付：返回h5_url，用于跳转支付页面
    """
    try:
        # 检查微信支付配置
        if not wechat_pay_service.is_configured():
            raise HTTPException(
                status_code=400, detail="微信支付未配置，请检查环境变量"
            )

        # 生成唯一订单号
        out_trade_no = f"WX_{int(time.time())}_{current_user.user_id}_{int(time.time() * 1000) % 10000}"

        # 调用微信支付API
        result = wechat_pay_service.create_order(
            description=order_data.description,
            out_trade_no=out_trade_no,
            total=order_data.total,
            trade_type=order_data.trade_type.value,
            openid=order_data.openid,
        )

        # 保存订单到数据库
        wechat_order = await WeChatOrderService.create_order(
            db=db,
            user_id=current_user.user_id,
            order_data=order_data,
            out_trade_no=out_trade_no,
            prepay_id=result.get("prepay_id"),
            code_url=result.get("code_url"),
            h5_url=result.get("mweb_url"),
        )

        # 构建响应数据
        response_data = WeChatPaymentResponse(
            order_id=wechat_order.order_id,
            out_trade_no=out_trade_no,
            prepay_id=result.get("prepay_id"),
            code_url=result.get("code_url"),
            h5_url=result.get("mweb_url"),
            trade_type=order_data.trade_type,
            total=order_data.total,
            description=order_data.description,
            created_at=wechat_order.created_at,
        )

        logger.info(
            f"微信支付订单创建成功: {out_trade_no}, 用户: {current_user.user_id}"
        )

        return response_data

    except Exception as e:
        logger.error(f"创建微信支付订单失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/orders", summary="获取用户订单列表")
async def get_user_orders(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
    page: int = 1,
    size: int = 10,
):
    """
    获取当前用户的订单列表

    **参数说明：**
    - **page**: 页码，从1开始
    - **size**: 每页数量，最大100

    **返回数据：**
    - 订单列表，包含订单号、金额、状态、创建时间等信息
    """
    try:
        # 限制每页数量
        size = min(size, 100)
        offset = (page - 1) * size

        # 查询用户订单
        orders = await WeChatOrderService.get_user_orders(
            db, current_user.user_id, offset=offset, limit=size
        )

        # 统计总数
        total = await WeChatOrderService.count_user_orders(db, current_user.user_id)

        # 格式化订单数据
        order_list = []
        for order in orders:
            order_data = {
                "out_trade_no": order.out_trade_no,
                "description": order.description,
                "total_fee": order.total,  # 数据库字段名是 total
                "trade_state": order.trade_state.value,
                "trade_type": order.trade_type.value,
                "created_at": order.created_at.isoformat(),
                "success_time": order.success_time.isoformat()
                if order.success_time
                else None,
                "transaction_id": order.transaction_id,
            }
            order_list.append(order_data)

        return {
            "success": True,
            "data": {
                "orders": order_list,
                "pagination": {
                    "page": page,
                    "size": size,
                    "total": total,
                    "pages": (total + size - 1) // size,
                },
            },
        }

    except Exception as e:
        logger.error(f"获取用户订单列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get(
    "/query-order/{out_trade_no}",
    response_model=WeChatOrderQuery,
    summary="查询订单状态",
)
async def query_wechat_order(
    out_trade_no: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    查询微信支付订单状态（用户只能查询自己的订单）

    **参数说明：**
    - **out_trade_no**: 商户订单号

    **订单状态说明：**
    - **SUCCESS**: 支付成功
    - **REFUND**: 转入退款
    - **NOTPAY**: 未支付
    - **CLOSED**: 已关闭
    - **REVOKED**: 已撤销（付款码支付）
    - **USERPAYING**: 用户支付中
    - **PAYERROR**: 支付失败
    """
    try:
        # 检查微信支付配置
        if not wechat_pay_service.is_configured():
            raise HTTPException(
                status_code=400, detail="微信支付未配置，请检查环境变量"
            )

        # 先从数据库查询订单，确保用户只能查询自己的订单
        db_order = await WeChatOrderService.get_order_by_trade_no(
            db, out_trade_no, current_user.user_id
        )

        if not db_order:
            raise HTTPException(status_code=404, detail="订单不存在或无权限访问")

        # 查询微信支付状态
        result = wechat_pay_service.query_order(out_trade_no)

        # 如果微信状态与数据库状态不一致，更新数据库
        if result["trade_state"] != db_order.trade_state.value:
            from app.models.wechat_order import WeChatTradeState
            from app.schemas.wechat_order import WeChatOrderUpdate

            update_data = WeChatOrderUpdate(
                trade_state=WeChatTradeState(result["trade_state"])
            )

            if result["trade_state"] == "SUCCESS":
                update_data.transaction_id = result.get("transaction_id")
                update_data.success_time = datetime.utcnow()

            await WeChatOrderService.update_order(db, out_trade_no, update_data)

        # 构建响应数据
        response_data = WeChatOrderQuery(
            out_trade_no=result["out_trade_no"],
            transaction_id=result.get("transaction_id"),
            trade_state=result["trade_state"],
            trade_state_desc=result.get("trade_state_desc", ""),
            success_time=result.get("time_end"),
            total=int(result["total_fee"]),
        )

        logger.info(
            f"查询微信支付订单: {out_trade_no}, 状态: {result['trade_state']}, 用户: {current_user.user_id}"
        )

        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询微信支付订单失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/refund", summary="申请退款")
async def refund_wechat_order(
    refund_data: WeChatRefundCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    申请微信支付退款

    **参数说明：**
    - **out_trade_no**: 原商户订单号
    - **out_refund_no**: 商户退款单号（必须唯一）
    - **refund_fee**: 退款金额，单位为分
    - **total_fee**: 原订单金额，单位为分
    - **reason**: 退款原因（可选）

    **注意事项：**
    - 退款金额不能超过原订单金额
    - 只有支付成功的订单才能申请退款
    - 退款单号必须唯一，不能重复使用
    """
    try:
        # 检查微信支付配置
        if not wechat_pay_service.is_configured():
            raise HTTPException(
                status_code=400, detail="微信支付未配置，请检查环境变量"
            )

        # 验证订单所有权
        db_order = await WeChatOrderService.get_order_by_trade_no(
            db, refund_data.out_trade_no, current_user.user_id
        )

        if not db_order:
            raise HTTPException(status_code=404, detail="订单不存在或无权限访问")

        # 检查订单状态
        if db_order.trade_state.value != "SUCCESS":
            raise HTTPException(
                status_code=400, detail="只有支付成功的订单才能申请退款"
            )

        # 申请退款
        result = wechat_pay_service.refund_order(
            out_trade_no=refund_data.out_trade_no,
            out_refund_no=refund_data.out_refund_no,
            refund=refund_data.refund_fee,
            total=refund_data.total_fee,
            reason=refund_data.reason,
        )

        # 保存退款记录到数据库
        await WeChatOrderService.create_refund(
            db=db, wechat_order_id=db_order.order_id, refund_data=refund_data
        )

        logger.info(
            f"微信支付退款申请成功: {refund_data.out_refund_no}, 用户: {current_user.user_id}"
        )

        return {"success": True, "message": "退款申请成功", "data": result}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"申请微信支付退款失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/notify", summary="微信支付回调通知")
async def wechat_pay_notify(request: Request, db: AsyncSession = Depends(get_db)):
    """
    处理微信支付结果通知

    **说明：**
    - 这个接口用于接收微信支付的异步通知
    - 微信会在支付完成后主动调用此接口
    - 接口会验证签名确保数据安全
    - 处理成功后会更新订单状态

    **回调时机：**
    - 支付成功
    - 支付失败
    - 退款成功
    """
    try:
        # 获取请求体
        body = await request.body()
        xml_data = body.decode("utf-8")

        logger.info("收到微信支付回调通知")

        # 验证签名并解析数据
        result = wechat_pay_service.verify_callback(xml_data)

        # 处理支付结果
        if result.get("result_code") == "SUCCESS":
            out_trade_no = result.get("out_trade_no")
            transaction_id = result.get("transaction_id")

            logger.info(
                f"微信支付成功: 订单号 {out_trade_no}, 微信订单号 {transaction_id}"
            )

            # 更新订单状态到数据库
            try:
                success_time = datetime.utcnow()
                if result.get("time_end"):
                    # 解析微信返回的时间格式：20230101120000
                    time_str = result.get("time_end")
                    success_time = datetime.strptime(time_str, "%Y%m%d%H%M%S")

                updated_order = await WeChatOrderService.update_payment_success(
                    db, out_trade_no, transaction_id, success_time
                )

                if updated_order:
                    logger.info(f"订单状态更新成功: {out_trade_no}")

                    # TODO: 这里可以添加更多业务逻辑：
                    # 1. 自动生成密钥
                    # 2. 发送通知邮件给用户
                    # 3. 更新库存
                    # 4. 记录操作日志等
                else:
                    logger.warning(f"订单状态更新失败，订单不存在: {out_trade_no}")

            except Exception as e:
                logger.error(f"处理支付成功回调时发生错误: {str(e)}")
                # 即使数据库更新失败，也要返回成功给微信，避免重复回调

        # 返回成功响应给微信
        return """<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>"""

    except Exception as e:
        logger.error(f"处理微信支付回调失败: {str(e)}")
        # 返回失败响应给微信，微信会重试
        return """<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[ERROR]]></return_msg></xml>"""


@router.post(
    "/jsapi-params", response_model=WeChatJSAPIParams, summary="生成JSAPI支付参数"
)
async def generate_jsapi_params(
    prepay_id: str, current_user: User = Depends(get_current_active_user)
):
    """
    生成JSAPI支付参数

    **说明：**
    - 用于在微信公众号内调起支付
    - 需要先调用创建订单接口获取prepay_id
    - 返回的参数用于前端调用微信支付API

    **参数说明：**
    - **prepay_id**: 预支付交易会话标识

    **返回参数用法：**
    ```javascript
    wx.chooseWXPay({
        timestamp: result.timeStamp,
        nonceStr: result.nonceStr,
        package: result.package,
        signType: result.signType,
        paySign: result.paySign,
        success: function (res) {
            // 支付成功
        }
    });
    ```
    """
    try:
        # 检查微信支付配置
        if not wechat_pay_service.is_configured():
            raise HTTPException(
                status_code=400, detail="微信支付未配置，请检查环境变量"
            )

        # 生成JSAPI支付参数
        result = wechat_pay_service.generate_jsapi_params(prepay_id)

        # 构建响应数据
        response_data = WeChatJSAPIParams(**result)

        logger.info(f"生成JSAPI支付参数成功: prepay_id={prepay_id}")

        return response_data

    except Exception as e:
        logger.error(f"生成JSAPI支付参数失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/config", response_model=WeChatPaymentConfig, summary="获取支付配置信息")
async def get_wechat_pay_config():
    """
    获取微信支付配置信息

    **返回信息：**
    - 支持的支付方式
    - 商户信息
    - API版本信息
    """
    try:
        logger.info("获取微信支付配置信息")
        logger.debug(f"当前DEBUG_LEVEL: {wechat_pay_service.is_configured()}")
        config_info = WeChatPaymentConfig(
            api_version="V2",
            app_id=wechat_pay_service.appid,
            mch_id=wechat_pay_service.mch_id,
            is_configured=wechat_pay_service.is_configured(),
            supported_trade_types=[
                {
                    "type": "JSAPI",
                    "name": "公众号支付",
                    "description": "在微信公众号内调起支付",
                },
                {
                    "type": "NATIVE",
                    "name": "扫码支付",
                    "description": "生成二维码，用户扫码支付",
                },
                {
                    "type": "MWEB",
                    "name": "H5支付",
                    "description": "在手机浏览器内调起支付",
                },
                {
                    "type": "APP",
                    "name": "APP支付",
                    "description": "在移动应用内调起支付",
                },
            ],
        )

        return config_info

    except Exception as e:
        logger.error(f"获取微信支付配置失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/close-order", summary="关闭微信支付订单")
async def close_wechat_order(
    close_data: WeChatOrderClose,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    关闭微信支付订单

    **使用场景：**
    - 用户取消支付
    - 订单超时未支付
    - 商户主动关闭订单

    **参数说明：**
    - **out_trade_no**: 商户订单号

    **注意事项：**
    - 只能关闭未支付的订单
    - 关闭后订单无法再次支付
    - 关闭成功后会释放相关资源
    """
    try:
        # 检查微信支付配置
        if not wechat_pay_service.is_configured():
            raise HTTPException(
                status_code=400, detail="微信支付未配置，请检查环境变量"
            )

        # 验证订单是否属于当前用户
        order = await WeChatOrderService.get_order_by_trade_no(
            db, close_data.out_trade_no
        )
        if not order:
            raise HTTPException(status_code=404, detail="订单不存在")

        if order.user_id != current_user.user_id:
            raise HTTPException(status_code=403, detail="无权操作此订单")

        # 检查订单状态
        if order.trade_state == "SUCCESS":
            raise HTTPException(status_code=400, detail="已支付的订单无法关闭")

        if order.trade_state == "CLOSED":
            raise HTTPException(status_code=400, detail="订单已关闭")

        # 调用微信支付API关闭订单
        result = wechat_pay_service.close_order(close_data.out_trade_no)

        # 更新数据库订单状态
        from app.models.wechat_order import WeChatTradeState
        from app.schemas.wechat_order import WeChatOrderUpdate

        update_data = WeChatOrderUpdate(trade_state=WeChatTradeState.CLOSED)
        await WeChatOrderService.update_order(db, close_data.out_trade_no, update_data)

        logger.info(
            f"微信支付订单关闭成功: {close_data.out_trade_no}, 用户: {current_user.user_id}"
        )

        return {
            "success": True,
            "message": "订单关闭成功",
            "out_trade_no": close_data.out_trade_no,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"关闭微信支付订单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/query-refund", summary="查询微信支付退款状态")
async def query_wechat_refund(
    refund_query: WeChatRefundQuery,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    查询微信支付退款状态

    **参数说明：**
    - **out_refund_no**: 商户退款单号

    **退款状态说明：**
    - **SUCCESS**: 退款成功
    - **REFUNDCLOSE**: 退款关闭
    - **PROCESSING**: 退款处理中
    - **CHANGE**: 退款异常，退款到银行发现用户的卡作废或者冻结了，导致原路退款银行卡失败，可前往商户平台（pay.weixin.qq.com）-交易中心，手动处理此笔退款
    """
    try:
        # 检查微信支付配置
        if not wechat_pay_service.is_configured():
            raise HTTPException(
                status_code=400, detail="微信支付未配置，请检查环境变量"
            )

        # 验证退款单是否属于当前用户
        refund = await WeChatOrderService.get_refund_by_refund_no(
            db, refund_query.out_refund_no
        )
        if not refund:
            raise HTTPException(status_code=404, detail="退款单不存在")

        # 通过退款单获取订单信息，验证用户权限
        order = await WeChatOrderService.get_order_by_id(db, refund.wechat_order_id)
        if not order or order.user_id != current_user.user_id:
            raise HTTPException(status_code=403, detail="无权查询此退款单")

        # 调用微信支付API查询退款状态
        result = wechat_pay_service.query_refund(refund_query.out_refund_no)

        # 更新数据库退款状态
        if result.get("refund_status_0"):
            from app.schemas.wechat_order import WeChatRefundUpdate

            update_data = WeChatRefundUpdate(
                refund_status=result.get("refund_status_0"),
                refund_id_wx=result.get("refund_id_0"),
            )

            # 如果退款成功，记录成功时间
            if result.get("refund_status_0") == "SUCCESS" and result.get(
                "refund_success_time_0"
            ):
                from datetime import datetime

                try:
                    # 解析微信返回的时间格式：20230101120000
                    time_str = result.get("refund_success_time_0")
                    success_time = datetime.strptime(time_str, "%Y%m%d%H%M%S")
                    update_data.success_time = success_time
                except:
                    pass

            await WeChatOrderService.update_refund(
                db, refund_query.out_refund_no, update_data
            )

        logger.info(
            f"微信支付退款查询成功: {refund_query.out_refund_no}, 用户: {current_user.user_id}"
        )

        return {
            "success": True,
            "refund_status": result.get("refund_status_0", "UNKNOWN"),
            "out_refund_no": refund_query.out_refund_no,
            "refund_id": result.get("refund_id_0"),
            "refund_fee": int(result.get("refund_fee_0", 0)),
            "settlement_refund_fee": int(result.get("settlement_refund_fee_0", 0)),
            "refund_success_time": result.get("refund_success_time_0"),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询微信支付退款失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
