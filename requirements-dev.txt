# FastAPI Development Dependencies
# Includes production dependencies + development tools

# First install production dependencies
-r requirements.txt

# Development and Testing Tools
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-cov>=4.0.0

# Code Quality Tools
ruff>=0.1.0
pre-commit>=3.0.0
mypy>=1.8.0

# Type Checking
types-ujson>=5.10.0
types-orjson>=3.6.2

# Documentation Tools
mkdocs>=1.5.0
mkdocs-material>=9.0.0

# Other Development Tools
black>=24.0.0  # Code formatting
coverage>=6.5.0  # Test coverage
tomli>=2.0.0  # TOML support for Python < 3.11

# Debugging and Performance
ipython>=8.0.0  # Interactive Python
rich>=13.0.0  # Rich output formatting
