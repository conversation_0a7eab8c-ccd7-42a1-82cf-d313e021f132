-- 软件授权系统数据库表创建脚本
-- 基于数据库设计规范，适用于Supabase PostgreSQL数据库

-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建枚举类型 - 使用大写值与Python模型保持一致
CREATE TYPE IF NOT EXISTS userstatus AS ENUM ('ACTIVE', 'BANNED', 'PENDING');
CREATE TYPE IF NOT EXISTS userrole AS ENUM ('USER', 'ADMIN', 'SUPER_ADMIN');
CREATE TYPE IF NOT EXISTS keystatus AS ENUM ('ACTIVE', 'EXPIRED', 'DISABLED');
CREATE TYPE IF NOT EXISTS paymenttype AS ENUM ('PURCHASE', 'REFUND');
CREATE TYPE IF NOT EXISTS wechattradetype AS ENUM ('JSAPI', 'NATIVE', 'APP', 'MWEB');
CREATE TYPE IF NOT EXISTS wechattradestate AS ENUM ('SUCCESS', 'REFUND', 'NOTPAY', 'CLOSED', 'REVOKED', 'USERPAYING', 'PAYERROR');

-- 注册用户表 (users)
CREATE TABLE IF NOT EXISTS users (
    user_id BIGSERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    status userstatus DEFAULT 'PENDING',
    role userrole DEFAULT 'USER',
    register_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login_date TIMESTAMP WITH TIME ZONE,
    nickname VARCHAR(50),
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_code VARCHAR(6),
    email_verification_expires TIMESTAMP WITH TIME ZONE,
    remarks TEXT
);

-- 产品表 (products)
CREATE TABLE IF NOT EXISTS products (
    product_id BIGSERIAL PRIMARY KEY,
    product_name VARCHAR(100) NOT NULL,
    description TEXT,
    default_price DECIMAL(10,2),
    created_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 秘钥表 (keys)
CREATE TABLE IF NOT EXISTS keys (
    key_id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    product_id BIGINT REFERENCES products(product_id) ON DELETE SET NULL,
    key_string VARCHAR(255) UNIQUE NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    activated_count INTEGER DEFAULT 0,
    max_activation_count INTEGER NOT NULL,
    status keystatus DEFAULT 'ACTIVE',
    created_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 秘钥激活记录表 (key_activations)
CREATE TABLE IF NOT EXISTS key_activations (
    activation_id BIGSERIAL PRIMARY KEY,
    key_id BIGINT NOT NULL REFERENCES keys(key_id) ON DELETE CASCADE,
    user_id BIGINT NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    device_id VARCHAR(255),
    ip_address VARCHAR(45),
    activation_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 支付记录表 (payment_records)
CREATE TABLE IF NOT EXISTS payment_records (
    payment_id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    payment_type paymenttype NOT NULL,
    payment_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    product_id BIGINT REFERENCES products(product_id) ON DELETE SET NULL,
    order_id VARCHAR(50),
    payment_method VARCHAR(50)
);

-- 用户操作日志表 (user_logs)
CREATE TABLE IF NOT EXISTS user_logs (
    log_id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    action VARCHAR(50) NOT NULL,
    action_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    details JSONB
);

-- 微信支付订单表 (wechat_orders)
CREATE TABLE IF NOT EXISTS wechat_orders (
    order_id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    product_id BIGINT REFERENCES products(product_id) ON DELETE SET NULL,
    payment_record_id BIGINT REFERENCES payment_records(payment_id) ON DELETE SET NULL,
    out_trade_no VARCHAR(32) UNIQUE NOT NULL,
    transaction_id VARCHAR(64),
    prepay_id VARCHAR(64),
    description VARCHAR(128) NOT NULL,
    total INTEGER NOT NULL,
    trade_type wechattradetype NOT NULL,
    trade_state wechattradestate DEFAULT 'NOTPAY',
    code_url TEXT,
    h5_url TEXT,
    openid VARCHAR(128),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    success_time TIMESTAMP WITH TIME ZONE,
    notify_received BOOLEAN DEFAULT FALSE,
    notify_time TIMESTAMP WITH TIME ZONE
);

-- 微信支付退款记录表 (wechat_refunds)
CREATE TABLE IF NOT EXISTS wechat_refunds (
    refund_id BIGSERIAL PRIMARY KEY,
    wechat_order_id BIGINT NOT NULL REFERENCES wechat_orders(order_id) ON DELETE CASCADE,
    out_refund_no VARCHAR(32) UNIQUE NOT NULL,
    refund_id_wx VARCHAR(64),
    refund_fee INTEGER NOT NULL,
    total_fee INTEGER NOT NULL,
    reason VARCHAR(256),
    refund_status VARCHAR(32),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    success_time TIMESTAMP WITH TIME ZONE
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_last_login ON users(last_login_date);

CREATE INDEX IF NOT EXISTS idx_keys_user_id ON keys(user_id);
CREATE INDEX IF NOT EXISTS idx_keys_product_id ON keys(product_id);
CREATE INDEX IF NOT EXISTS idx_keys_key_string ON keys(key_string);

CREATE INDEX IF NOT EXISTS idx_key_activations_key_id ON key_activations(key_id);
CREATE INDEX IF NOT EXISTS idx_key_activations_activation_date ON key_activations(activation_date);

CREATE INDEX IF NOT EXISTS idx_payment_records_user_id ON payment_records(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_records_payment_date ON payment_records(payment_date);

CREATE INDEX IF NOT EXISTS idx_user_logs_user_id ON user_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_user_logs_action_date ON user_logs(action_date);

CREATE INDEX IF NOT EXISTS idx_wechat_orders_user_id ON wechat_orders(user_id);
CREATE INDEX IF NOT EXISTS idx_wechat_orders_out_trade_no ON wechat_orders(out_trade_no);
CREATE INDEX IF NOT EXISTS idx_wechat_orders_transaction_id ON wechat_orders(transaction_id);
CREATE INDEX IF NOT EXISTS idx_wechat_orders_created_at ON wechat_orders(created_at);
CREATE INDEX IF NOT EXISTS idx_wechat_orders_trade_state ON wechat_orders(trade_state);

CREATE INDEX IF NOT EXISTS idx_wechat_refunds_wechat_order_id ON wechat_refunds(wechat_order_id);
CREATE INDEX IF NOT EXISTS idx_wechat_refunds_out_refund_no ON wechat_refunds(out_refund_no);

-- 创建更新时间戳的触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要自动更新时间戳的表创建触发器
CREATE TRIGGER update_keys_updated_date BEFORE UPDATE ON keys
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_wechat_orders_updated_at BEFORE UPDATE ON wechat_orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 启用行级安全策略（RLS）
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE key_activations ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE wechat_orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE wechat_refunds ENABLE ROW LEVEL SECURITY;

-- 创建基本的RLS策略（用户只能访问自己的数据）
CREATE POLICY "Users can view own profile" ON users
    FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid()::text = user_id::text);

-- 产品表允许所有用户查看

CREATE POLICY "All users can view products" ON products
    FOR SELECT USING (true);

-- 秘钥相关策略
CREATE POLICY "Users can view own keys" ON keys
    FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can manage own keys" ON keys
    FOR ALL USING (auth.uid()::text = user_id::text);

-- 激活记录策略
CREATE POLICY "Users can view own activations" ON key_activations
    FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can manage own activations" ON key_activations
    FOR ALL USING (auth.uid()::text = user_id::text);

-- 支付记录策略
CREATE POLICY "Users can view own payments" ON payment_records
    FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can manage own payments" ON payment_records
    FOR ALL USING (auth.uid()::text = user_id::text);

-- 用户日志策略
CREATE POLICY "Users can view own logs" ON user_logs
    FOR SELECT USING (auth.uid()::text = user_id::text);

-- 微信支付订单策略
CREATE POLICY "Users can view own wechat orders" ON wechat_orders
    FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can manage own wechat orders" ON wechat_orders
    FOR ALL USING (auth.uid()::text = user_id::text);

-- 微信支付退款策略
CREATE POLICY "Users can view own wechat refunds" ON wechat_refunds
    FOR SELECT USING (auth.uid()::text = (SELECT user_id::text FROM wechat_orders WHERE order_id = wechat_refunds.wechat_order_id));

CREATE POLICY "Users can manage own wechat refunds" ON wechat_refunds
    FOR ALL USING (auth.uid()::text = (SELECT user_id::text FROM wechat_orders WHERE order_id = wechat_refunds.wechat_order_id));
