"""
API路由管理模块
用于统一管理API路由
如果不显示的话可以添加   include_in_schema=False
"""

from fastapi import APIRouter

from app.api import admin, auth, legacy, products, users, wechat_pay
from app.api.dashboard import router as dashboard_router
from app.core.config import settings


def create_api_router() -> APIRouter:
    """创建主API路由器"""
    api_router = APIRouter(prefix=settings.API_BASE_STR)

    # 注册API路由
    api_router.include_router(auth.router, prefix="/auth", tags=["Authentication"])
    api_router.include_router(users.router, prefix="/users", tags=["Users"])
    api_router.include_router(
        products.router, prefix="/products", tags=["Products"], include_in_schema=False
    )
    api_router.include_router(admin.router, prefix="/admin", tags=["Admin Management"])
    api_router.include_router(
        legacy.router,
        prefix="/legacy",
        tags=["Legacy Integration"],
        include_in_schema=False,
    )
    api_router.include_router(
        wechat_pay.router, prefix="/wechat-pay", tags=["WeChat Payment"]
    )
    api_router.include_router(
        dashboard_router, prefix="/dashboard", tags=["Dashboard - Users"]
    )

    return api_router


def create_legacy_router() -> APIRouter:
    """创建向后兼容的旧版本路由器（不在文档中显示）"""
    legacy_router = APIRouter()

    # 注册旧版本路由（向后兼容）
    legacy_router.include_router(
        auth.router,
        prefix="/auth",
        tags=["Authentication (Legacy)"],
        include_in_schema=False,
    )
    legacy_router.include_router(
        users.router,
        prefix="/user",  # 注意：旧版本是 /user 不是 /users
        tags=["Users (Legacy)"],
        include_in_schema=False,
    )
    legacy_router.include_router(
        products.router,
        prefix="/products",
        tags=["Products (Legacy)"],
        include_in_schema=False,
    )

    return legacy_router


# API信息
def get_api_info():
    """获取API信息"""
    return {
        "name": "Fast MetaTrader Auth API",
        "version": "1.0.0",
        "description": "API with complete authentication, user and product management features",
        "status": "stable",
        "base_url": settings.API_BASE_STR,
        "docs_url": "/docs",
        "redoc_url": "/redoc",
    }
