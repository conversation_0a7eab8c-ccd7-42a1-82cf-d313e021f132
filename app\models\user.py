"""
用户相关数据模型
"""

import enum
from datetime import datetime
from typing import TYPE_CHECKING, Optional

from sqlalchemy import <PERSON>ole<PERSON>, DateTime, Enum, Integer, String, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.db.database import Base

if TYPE_CHECKING:
    from .key import Key, KeyActivation
    from .log import UserLog
    from .payment import PaymentRecord
    from .wechat_order import WeChatOrder


class UserStatus(enum.Enum):
    """
    用户状态枚举
    统一规范：常量名和值都使用大写，确保数据库和代码的一致性
    """

    ACTIVE = "ACTIVE"  # 活跃用户
    BANNED = "BANNED"  # 被封禁用户
    PENDING = "PENDING"  # 待激活用户


class UserRole(enum.Enum):
    """
    用户角色枚举
    统一规范：常量名和值都使用大写，确保数据库和代码的一致性
    """

    USER = "USER"  # 普通用户
    ADMIN = "ADMIN"  # 管理员
    SUPER_ADMIN = "SUPER_ADMIN"  # 超级管理员（环境变量配置）


class User(Base):
    """注册用户表 - 根据数据库设计规范"""

    __tablename__ = "users"

    user_id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    email: Mapped[str] = mapped_column(
        String(255), unique=True, nullable=False, index=True
    )
    password_hash: Mapped[str] = mapped_column(String(255), nullable=False)
    status: Mapped[UserStatus] = mapped_column(
        Enum(UserStatus), default=UserStatus.PENDING
    )
    role: Mapped[UserRole] = mapped_column(Enum(UserRole), default=UserRole.USER)
    register_date: Mapped[datetime] = mapped_column(
        DateTime, default=lambda: datetime.utcnow()
    )
    last_login_date: Mapped[Optional[datetime]] = mapped_column(DateTime, index=True)
    nickname: Mapped[Optional[str]] = mapped_column(String(50))
    email_verified: Mapped[bool] = mapped_column(Boolean, default=False)
    # 邮箱验证码相关字段
    email_verification_code: Mapped[Optional[str]] = mapped_column(
        String(6), nullable=True
    )
    email_verification_expires: Mapped[Optional[datetime]] = mapped_column(
        DateTime, nullable=True
    )
    remarks: Mapped[Optional[str]] = mapped_column(Text)

    # 关系
    keys: Mapped[list["Key"]] = relationship("Key", back_populates="user")
    payment_records: Mapped[list["PaymentRecord"]] = relationship(
        "PaymentRecord", back_populates="user"
    )
    user_logs: Mapped[list["UserLog"]] = relationship("UserLog", back_populates="user")
    key_activations: Mapped[list["KeyActivation"]] = relationship(
        "KeyActivation", back_populates="user"
    )
    wechat_orders: Mapped[list["WeChatOrder"]] = relationship(
        "WeChatOrder", back_populates="user"
    )
