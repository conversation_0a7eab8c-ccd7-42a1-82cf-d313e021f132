"""
产品相关Pydantic模式定义
"""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict, Field


# 产品相关模式
class ProductBase(BaseModel):
    """产品基础模式"""

    product_name: str = Field(..., max_length=100)
    description: Optional[str] = None
    default_price: Optional[float] = Field(None, ge=0)


class ProductCreate(ProductBase):
    """产品创建模式"""

    pass


class ProductUpdate(BaseModel):
    """产品更新模式"""

    product_name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = None
    default_price: Optional[float] = Field(None, ge=0)


class ProductResponse(ProductBase):
    """产品响应模式"""

    model_config = ConfigDict(from_attributes=True)

    product_id: int
    created_date: datetime
