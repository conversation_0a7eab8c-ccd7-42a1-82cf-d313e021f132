"""
Pydantic模式模块
"""

from .common import (
    AuthResponse,
    HealthResponse,
    PaginatedResponse,
    PaginationParams,
    Token,
)
from .key import (
    KeyActivationCreate,
    KeyActivationResponse,
    KeyCreate,
    KeyResponse,
    KeyUpdate,
)
from .log import UserLogCreate, UserLogResponse
from .payment import PaymentRecordCreate, PaymentRecordResponse
from .product import ProductCreate, ProductResponse, ProductUpdate
from .user import UserCreate, UserLogin, UserResponse, UserUpdate

__all__ = [
    # 用户相关
    "UserCreate",
    "UserUpdate",
    "UserResponse",
    "UserLogin",
    # 产品相关
    "ProductCreate",
    "ProductUpdate",
    "ProductResponse",
    # 秘钥相关
    "KeyCreate",
    "KeyUpdate",
    "KeyResponse",
    "KeyActivationCreate",
    "KeyActivationResponse",
    # 支付相关
    "PaymentRecordCreate",
    "PaymentRecordResponse",
    # 日志相关
    "UserLogCreate",
    "UserLogResponse",
    # 通用
    "HealthResponse",
    "AuthResponse",
    "Token",
    "PaginationParams",
    "PaginatedResponse",
]
