# FastAuth_std - 企业级软件授权系统 API 文档

基于 FastAPI 框架的企业级软件授权管理系统，提供完整的用户管理、产品管理、密钥管理和管理员系统功能。

## 🌟 系统特性

### 🔥 核心功能
- ✅ **完整的用户认证系统** - 注册、登录、邮箱验证
- ✅ **企业级邮件服务** - 真实SMTP邮件发送，支持验证码
- ✅ **智能验证码系统** - 6位数字验证码，15分钟有效期，支持重发
- ✅ **多层权限管理** - 用户、管理员、超级管理员三级权限
- ✅ **产品密钥管理** - 完整的软件授权密钥生命周期管理
- ✅ **数据库管理工具** - 一键初始化、状态监控、健康检查

### 🚀 技术优势
- ✅ **零并发冲突** - 纯asyncpg架构，完美兼容pgbouncer
- ✅ **Vercel优化** - 专为serverless环境优化的无状态架构
- ✅ **Supabase深度集成** - 企业级PostgreSQL数据库支持
- ✅ **高性能设计** - 支持无限并发用户注册和登录
- ✅ **完整的错误处理** - 用户友好的错误信息和恢复机制

### 🛡️ 安全特性
- ✅ **JWT令牌认证** - 安全的无状态认证机制
- ✅ **密码哈希存储** - bcrypt加密，安全可靠
- ✅ **速率限制保护** - 防止暴力破解和滥用
- ✅ **邮箱验证机制** - 确保用户邮箱真实有效
- ✅ **多重权限验证** - 细粒度的权限控制系统

## 📋 目录

- [系统概述](#系统概述)
- [认证方式](#认证方式)
- [API 端点总览](#api-端点总览)
- [认证相关 API](#认证相关-api)
- [用户管理 API](#用户管理-api)
- [产品管理 API](#产品管理-api)
- [微信支付 API](#微信支付-api)
- [管理员 API](#管理员-api)
- [Legacy Integration API](#legacy-integration-api)
- [系统端点](#系统端点)
- [错误处理](#错误处理)
- [速率限制](#速率限制)

## 🔍 系统概述

### 基本信息
- **应用名称**: FastAPI 软件授权系统
- **API 版本**: 1.0.0
- **基础路径**: `/api`
- **文档地址**: `/docs` (Swagger UI), `/redoc` (ReDoc)

### 访问模式
系统支持多种访问模式，通过环境变量 `USER_ACCESS_MODE` 配置：
- **模式 0**: 完全开放 - 允许用户注册和登录
- **模式 1**: 仅登录 - 禁用注册，仅允许登录
- **模式 2**: 管理员模式 - 仅管理员可访问

## 🔐 认证方式

### Bearer Token 认证
所有需要认证的端点都使用 Bearer Token 认证：
```
Authorization: Bearer <access_token>
```

### OAuth2 密码认证
支持 OAuth2 密码流认证，用于 Swagger UI 等工具：
- 端点: `/api/auth/token`
- 用户名: 邮箱地址
- 密码: 用户密码

## 📊 API 端点总览

### 认证相关 (Authentication)
| 方法 | 端点 | 描述 | 认证 |
|------|------|------|------|
| POST | `/api/auth/send-register-code` | 发送注册验证码 | ❌ |
| POST | `/api/auth/register` | 用户注册（需验证码） | ❌ |
| POST | `/api/auth/login` | 用户登录 | ❌ |
| POST | `/api/auth/token` | OAuth2 令牌获取 | ❌ |
| POST | `/api/auth/verify-email` | 邮箱验证 | ❌ |
| POST | `/api/auth/resend-verification` | 重发验证码 | ❌ |
| GET | `/api/auth/me` | 获取当前用户信息 | ✅ |

### 用户管理 (Users)
| 方法 | 端点 | 描述 | 认证 |
|------|------|------|------|
| GET | `/api/users/profile` | 获取用户资料 | ✅ |
| GET | `/api/users/keys` | 获取用户密钥列表 | ✅ |

### 产品管理 (Products)
| 方法 | 端点 | 描述 | 认证 |
|------|------|------|------|
| GET | `/api/products/` | 获取产品列表 | ✅ |

### 微信支付相关 (WeChat Payment)
| 方法 | 端点 | 描述 | 认证 |
|------|------|------|------|
| POST | `/api/wechat-pay/create-order` | 创建支付订单 | ✅ |
| GET | `/api/wechat-pay/orders` | 获取用户订单列表 | ✅ |
| GET | `/api/wechat-pay/query-order/{out_trade_no}` | 查询订单状态 | ✅ |
| POST | `/api/wechat-pay/refund` | 申请退款 | ✅ |
| POST | `/api/wechat-pay/notify` | 微信支付回调通知 | ❌ |
| POST | `/api/wechat-pay/jsapi-params` | 生成JSAPI支付参数 | ✅ |
| GET | `/api/wechat-pay/config` | 获取支付配置信息 | ❌ |
| POST | `/api/wechat-pay/close-order` | 关闭微信支付订单 | ✅ |
| POST | `/api/wechat-pay/query-refund` | 查询微信支付退款状态 | ✅ |

### 管理员管理 (Admin)
| 方法 | 端点 | 描述 | 认证 |
|------|------|------|------|
| POST | `/api/admin/login` | 管理员登录 | ❌ |
| POST | `/api/admin/token` | 管理员 OAuth2 令牌 | ❌ |
| GET | `/api/admin/info` | 获取管理员信息 | ✅ |
| GET | `/api/admin/me` | 获取当前管理员信息 | ✅ |
| POST | `/api/admin/create` | 创建管理员 | ✅ (超级管理员) |
| GET | `/api/admin/list` | 获取管理员列表 | ✅ |
| GET | `/api/admin/stats` | 获取系统统计 | ✅ |
| GET | `/api/admin/users` | 获取用户列表 | ✅ |
| PUT | `/api/admin/users/{user_id}/status` | 更新用户状态 | ✅ |
| GET | `/api/admin/service-status` | 获取服务状态 | ✅ |
| POST | `/api/admin/service-status` | 更新服务状态 | ✅ (超级管理员) |
| POST | `/api/admin/database/init` | 初始化数据库表结构 | ✅ (超级管理员) |
| GET | `/api/admin/database/status` | 获取数据库状态信息 | ✅ |

### Legacy Integration
| 方法 | 端点 | 描述 | 认证 |
|------|------|------|------|
| POST | `/api/legacy/check/register` | 检查账号注册状态 | ❌ |
| POST | `/api/legacy/login` | 原网站用户登录 | ❌ |
| POST | `/api/legacy/register` | 原网站用户注册 | ❌ |
| GET | `/api/legacy/status` | Legacy 系统状态 | ❌ |

### 系统端点
| 方法 | 端点 | 描述 | 认证 |
|------|------|------|------|
| GET | `/` | 系统根路径信息 | ❌ |
| GET | `/health` | 健康检查 | ❌ |
| GET | `/favicon.ico` | 网站图标 | ❌ |

## 🔑 认证相关 API

### 发送注册验证码

**新的两步注册流程**：用户需要先发送验证码到邮箱，然后使用验证码完成注册。

```http
POST /api/auth/send-register-code
Content-Type: application/x-www-form-urlencoded

email=<EMAIL>
```

**响应示例**:
```json
{
  "success": true,
  "message": "Verification code <NAME_EMAIL>",
  "verification_code": "123456"
}
```

**功能特性**:
- ✅ **智能重发机制** - 如果用户未收到邮件，可以重复调用此端点
- ✅ **真实邮件发送** - 验证码通过SMTP发送到用户邮箱
- ✅ **15分钟有效期** - 验证码自动过期，确保安全性
- ✅ **6位数字验证码** - 易于输入和记忆
- ✅ **防重复注册** - 只检查已激活用户，允许PENDING用户重发

**错误响应**:
```json
{
  "detail": "Email already registered"
}
```

### 用户注册（需验证码）
```http
POST /api/auth/register
Content-Type: application/x-www-form-urlencoded

email=<EMAIL>&password=password123&verification_code=123456&nickname=用户昵称
```

**参数说明**:
- `email`: 用户邮箱地址（必须先调用发送验证码接口）
- `password`: 用户密码（最少8位字符）
- `verification_code`: 6位数字验证码（从邮箱获取）
- `nickname`: 用户昵称（可选）

**响应示例**:
```json
{
  "success": true,
  "message": "Registration successful",
  "token": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "expires_in": 43200
  },
  "user": {
    "user_id": 1,
    "email": "<EMAIL>",
    "nickname": "用户昵称",
    "status": "ACTIVE",
    "role": "USER"
  }
}
```

### 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "Login successful",
  "token": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "expires_in": 43200
  },
  "user": {
    "user_id": 1,
    "email": "<EMAIL>",
    "nickname": "用户昵称",
    "status": "ACTIVE",
    "role": "USER"
  }
}
```

### OAuth2 令牌获取
```http
POST /api/auth/token
Content-Type: application/x-www-form-urlencoded

username=<EMAIL>&password=password123&grant_type=password
```

**响应示例**:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 43200
}
```

### 邮箱验证
```http
POST /api/auth/verify-email
Content-Type: application/json

{
  "email": "<EMAIL>",
  "verification_code": "123456"
}
```

### 重发验证码
```http
POST /api/auth/resend-verification
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

### 获取当前用户信息
```http
GET /api/auth/me
Authorization: Bearer <access_token>
```

**响应示例**:
```json
{
  "user_id": 1,
  "email": "<EMAIL>",
  "nickname": "用户昵称",
  "status": "ACTIVE",
  "role": "USER",
  "register_date": "2024-01-01T00:00:00",
  "last_login_date": "2024-01-01T12:00:00",
  "email_verified": true
}
```

## 👤 用户管理 API

### 获取用户资料
```http
GET /api/users/profile
Authorization: Bearer <access_token>
```

**响应示例**:
```json
{
  "user_id": 1,
  "email": "<EMAIL>",
  "nickname": "用户昵称",
  "status": "ACTIVE",
  "role": "USER",
  "register_date": "2024-01-01T00:00:00",
  "last_login_date": "2024-01-01T12:00:00",
  "email_verified": true
}
```

### 获取用户密钥列表
```http
GET /api/users/keys
Authorization: Bearer <access_token>
```

**响应示例**:
```json
[
  {
    "key_id": 1,
    "key_value": "XXXX-XXXX-XXXX-XXXX",
    "product_id": 1,
    "user_id": 1,
    "status": "ACTIVE",
    "created_date": "2024-01-01T00:00:00",
    "expire_date": "2024-12-31T23:59:59"
  }
]
```

## 📦 产品管理 API

### 获取产品列表
```http
GET /api/products/
Authorization: Bearer <access_token>
```

**响应示例**:
```json
[
  {
    "product_id": 1,
    "product_name": "产品名称",
    "description": "产品描述",
    "default_price": 99.99,
    "created_date": "2024-01-01T00:00:00"
  }
]
```

## 💰 微信支付 API

### 创建支付订单
```http
POST /api/wechat-pay/create-order
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "product_id": 1,
  "amount": 99.99,
  "description": "产品购买",
  "notify_url": "https://your-domain.com/api/wechat-pay/notify"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "Payment order created successfully",
  "order_data": {
    "out_trade_no": "ORDER_20240101_123456",
    "prepay_id": "wx123456789",
    "code_url": "weixin://wxpay/bizpayurl?pr=abc123",
    "amount": 99.99,
    "status": "PENDING"
  }
}
```

### 获取用户订单列表
```http
GET /api/wechat-pay/orders?page=1&size=10
Authorization: Bearer <access_token>
```

**响应示例**:
```json
{
  "orders": [
    {
      "out_trade_no": "ORDER_20240101_123456",
      "amount": 99.99,
      "description": "产品购买",
      "status": "PAID",
      "created_at": "2024-01-01T12:00:00",
      "paid_at": "2024-01-01T12:05:00"
    }
  ],
  "total": 1,
  "page": 1,
  "size": 10
}
```

### 查询订单状态
```http
GET /api/wechat-pay/query-order/ORDER_20240101_123456
Authorization: Bearer <access_token>
```

**响应示例**:
```json
{
  "out_trade_no": "ORDER_20240101_123456",
  "transaction_id": "wx_transaction_123",
  "trade_state": "SUCCESS",
  "amount": 99.99,
  "paid_at": "2024-01-01T12:05:00"
}
```

### 申请退款
```http
POST /api/wechat-pay/refund
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "out_trade_no": "ORDER_20240101_123456",
  "refund_amount": 99.99,
  "refund_reason": "用户申请退款"
}
```

### 生成JSAPI支付参数
```http
POST /api/wechat-pay/jsapi-params
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "prepay_id": "wx123456789"
}
```

**响应示例**:
```json
{
  "appId": "wx1234567890",
  "timeStamp": "1640995200",
  "nonceStr": "abc123",
  "package": "prepay_id=wx123456789",
  "signType": "RSA",
  "paySign": "signature_string"
}
```

### 获取支付配置信息
```http
GET /api/wechat-pay/config
```

**响应示例**:
```json
{
  "app_id": "wx1234567890",
  "mch_id": "1234567890",
  "supported_payment_methods": ["JSAPI", "NATIVE", "APP"],
  "currency": "CNY"
}
```

### 关闭微信支付订单
```http
POST /api/wechat-pay/close-order
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "out_trade_no": "ORDER_20240101_123456"
}
```

### 查询微信支付退款状态
```http
POST /api/wechat-pay/query-refund
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "out_refund_no": "REFUND_20240101_123456"
}
```

**响应示例**:
```json
{
  "out_refund_no": "REFUND_20240101_123456",
  "refund_id": "wx_refund_123",
  "status": "SUCCESS",
  "amount": 99.99,
  "refund_time": "2024-01-01T15:00:00"
}
```

### 微信支付回调通知
```http
POST /api/wechat-pay/notify
Content-Type: application/xml

<xml>
  <return_code><![CDATA[SUCCESS]]></return_code>
  <return_msg><![CDATA[OK]]></return_msg>
  <!-- 微信支付回调数据 -->
</xml>
```

**说明**: 此端点用于接收微信支付的异步通知，无需认证，由微信服务器调用。

## 🛡️ 管理员 API

### 管理员登录
```http
POST /api/admin/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "admin123"
}
```

**响应示例**:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "admin_info": {
    "user_id": 1,
    "email": "<EMAIL>",
    "role": "ADMIN",
    "status": "ACTIVE",
    "is_super_admin": false,
    "source": "database"
  }
}
```

### 管理员 OAuth2 令牌
```http
POST /api/admin/token
Content-Type: application/x-www-form-urlencoded

username=<EMAIL>&password=admin123&grant_type=password
```

### 获取管理员信息
```http
GET /api/admin/info
Authorization: Bearer <admin_access_token>
```

**响应示例**:
```json
{
  "user_id": 1,
  "email": "<EMAIL>",
  "role": "ADMIN",
  "status": "ACTIVE",
  "is_super_admin": false,
  "source": "database",
  "register_date": "2024-01-01T00:00:00",
  "last_login_date": "2024-01-01T12:00:00"
}
```

### 创建管理员 (仅超级管理员)
```http
POST /api/admin/create
Authorization: Bearer <super_admin_token>
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "newadmin123",
  "role": "ADMIN"
}
```

### 获取管理员列表
```http
GET /api/admin/list
Authorization: Bearer <admin_access_token>
```

**响应示例**:
```json
[
  {
    "user_id": 1,
    "email": "<EMAIL>",
    "role": "ADMIN",
    "status": "ACTIVE",
    "register_date": "2024-01-01T00:00:00"
  }
]
```

### 获取系统统计
```http
GET /api/admin/stats
Authorization: Bearer <admin_access_token>
```

**响应示例**:
```json
{
  "total_users": 100,
  "active_users": 95,
  "total_admins": 5,
  "total_products": 10,
  "total_keys": 200,
  "active_keys": 180
}
```

### 获取用户列表
```http
GET /api/admin/users?skip=0&limit=50
Authorization: Bearer <admin_access_token>
```

**响应示例**:
```json
[
  {
    "user_id": 1,
    "email": "<EMAIL>",
    "nickname": "用户昵称",
    "status": "ACTIVE",
    "role": "USER",
    "register_date": "2024-01-01T00:00:00",
    "last_login_date": "2024-01-01T12:00:00"
  }
]
```

### 更新用户状态
```http
PUT /api/admin/users/1/status
Authorization: Bearer <admin_access_token>
Content-Type: application/json

{
  "new_status": "BANNED"
}
```

**响应示例**:
```json
{
  "message": "User status updated to: BANNED"
}
```

### 获取服务状态
```http
GET /api/admin/service-status
Authorization: Bearer <admin_access_token>
```

**响应示例**:
```json
{
  "current_mode": "OPEN",
  "current_mode_number": 0,
  "current_mode_description": "完全开放模式",
  "user_registration_allowed": true,
  "user_login_allowed": true,
  "available_modes": [
    {"mode": "OPEN", "number": 0, "description": "完全开放模式"},
    {"mode": "LOGIN_ONLY", "number": 1, "description": "仅登录模式"},
    {"mode": "ADMIN_ONLY", "number": 2, "description": "管理员模式"}
  ],
  "timestamp": "2024-01-01T12:00:00"
}
```

### 更新服务状态 (仅超级管理员)
```http
POST /api/admin/service-status
Authorization: Bearer <super_admin_token>
Content-Type: application/json

{
  "new_mode": "LOGIN_ONLY"
}
```

### 初始化数据库表结构 (仅超级管理员)
```http
POST /api/admin/database/init
Authorization: Bearer <super_admin_token>
Content-Type: application/json

{
  "force_recreate": false,
  "create_sample_data": true,
  "confirm_operation": true
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "Database initialization completed successfully. Created 6 new tables.",
  "details": {
    "database_type": "postgresql",
    "total_tables": 6,
    "new_tables_created": 6,
    "existing_tables_preserved": 0,
    "sample_data_created": true,
    "force_recreate_requested": false,
    "admin_email": "<EMAIL>"
  },
  "tables_created": [
    "users",
    "products",
    "keys",
    "key_activations",
    "payment_records",
    "user_logs"
  ],
  "warnings": [],
  "timestamp": "2024-01-01T12:00:00"
}
```

**参数说明**:
- `force_recreate`: 是否强制重新创建表（暂未实现）
- `create_sample_data`: 是否创建示例数据（如管理员账户）
- `confirm_operation`: 必须设为 `true` 才能执行（安全确认）

#### ⚠️ 云端部署故障排除

**问题**: 在Supabase等云端PostgreSQL环境中可能遇到 `Device or resource busy` 错误

**原因**: pgbouncer连接池环境中，事务锁定导致的资源竞争问题

**解决方案**:
- ✅ **已修复**: 系统已优化为使用非事务连接进行状态检查
- ✅ **自动处理**: 代码自动检测Supabase环境并应用最佳配置
- ✅ **连接池优化**: 针对云端环境调整了连接池参数

**技术细节**:
```python
# 修复前（可能导致锁定）
async with engine.begin() as conn:  # 事务连接
    result = await conn.execute(text("SELECT ..."))

# 修复后（避免锁定）
async with engine.connect() as conn:  # 非事务连接
    result = await conn.execute(text("SELECT ..."))
```

**如果仍遇到问题**:
1. 等待几分钟后重试（让连接池稳定）
2. 检查Supabase控制台是否有长时间运行的查询
3. 考虑升级到Supabase付费计划获得更好的性能

### 获取数据库状态信息
```http
GET /api/admin/database/status
Authorization: Bearer <admin_access_token>
```

**响应示例**:
```json
{
  "database_info": {
    "database_type": "postgresql",
    "database_url_configured": true,
    "engine_available": true,
    "supabase_available": true,
    "supabase_configured": true,
    "supabase_admin_configured": true
  },
  "tables_status": {
    "users": {
      "exists": true,
      "row_count": 5,
      "status": "healthy"
    },
    "products": {
      "exists": true,
      "row_count": 2,
      "status": "healthy"
    }
  },
  "total_defined_tables": 6,
  "tables_existing": 6,
  "tables_missing": 0,
  "timestamp": "2024-01-01T12:00:00"
}
```

## 🔄 Legacy Integration API

### 检查账号注册状态
```http
POST /api/legacy/check/register
Content-Type: application/json

{
  "account": "<EMAIL>"
}
```

**响应示例**:
```json
{
  "msg": "查询成功",
  "code": 200,
  "data": false
}
```

### 原网站用户登录
```http
POST /api/legacy/login
Content-Type: application/json

{
  "account": "<EMAIL>",
  "password": "password123",
  "remember": 1,
  "type": 0
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "Legacy登录成功，已自动创建本地账户",
  "token": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "expires_in": 43200
  },
  "user": {
    "user_id": 1,
    "email": "<EMAIL>",
    "status": "ACTIVE",
    "role": "USER"
  }
}
```

### 原网站用户注册 (不推荐)
```http
POST /api/legacy/register
Content-Type: application/json

{
  "account": "<EMAIL>",
  "password": "password123",
  "nickname": "新用户"
}
```

### Legacy 系统状态
```http
GET /api/legacy/status
```

**响应示例**:
```json
{
  "service": "Legacy Integration",
  "status": "active",
  "original_website": {
    "base_url": "https://www.00123.com/api/user",
    "connection": "ok",
    "timeout": 30.0
  },
  "features": {
    "account_check": "enabled",
    "legacy_login": "enabled",
    "auto_user_creation": "enabled",
    "legacy_register": "available_but_not_recommended"
  }
}
```

## 🏠 系统端点

### 根路径信息
```http
GET /
```

**响应示例**:
```json
{
  "message": "FastAPI 软件授权系统",
  "version": "1.0.0",
  "api_base_url": "/api",
  "docs": "/docs",
  "redoc": "/redoc",
  "authentication": {
    "access_mode": "OPEN",
    "access_mode_number": 0,
    "access_mode_description": "完全开放模式",
    "user_registration_allowed": true,
    "user_login_allowed": true,
    "admin_login": "/api/admin/login",
    "admin_token": "/api/admin/token",
    "user_registration": "/api/auth/register",
    "user_login": "/api/auth/login"
  },
  "notice": "完全开放模式"
}
```

### 健康检查
```http
GET /health
```

**响应示例**:
```json
{
  "status": "healthy",
  "message": "API is running successfully",
  "version": "1.0.0",
  "database_connected": true,
  "supabase_connected": false
}
```

## ❌ 错误处理

### 标准错误响应格式
所有API错误都遵循统一的响应格式：

```json
{
  "detail": "错误描述信息"
}
```

### 常见HTTP状态码

| 状态码 | 含义 | 示例场景 |
|--------|------|----------|
| 200 | 成功 | 请求成功处理 |
| 201 | 创建成功 | 用户注册成功 |
| 400 | 请求错误 | 参数验证失败 |
| 401 | 未认证 | 缺少或无效的访问令牌 |
| 403 | 权限不足 | 普通用户访问管理员端点 |
| 404 | 资源不存在 | 用户或产品不存在 |
| 422 | 验证错误 | 请求数据格式错误 |
| 429 | 请求过多 | 触发速率限制 |
| 500 | 服务器错误 | 内部服务器错误 |

### 验证错误详细格式
当请求数据验证失败时（422状态码），响应包含详细的错误信息：

```json
{
  "detail": [
    {
      "loc": ["body", "email"],
      "msg": "field required",
      "type": "value_error.missing"
    },
    {
      "loc": ["body", "password"],
      "msg": "ensure this value has at least 8 characters",
      "type": "value_error.any_str.min_length"
    }
  ]
}
```

## ⏱️ 速率限制

系统实现了多层速率限制保护：

### 认证相关限制
- **用户注册**: 每IP每小时最多5次
- **用户登录**: 每IP每小时最多10次
- **OAuth2令牌**: 每IP每小时最多20次
- **邮箱验证**: 每IP每小时最多10次
- **重发验证码**: 每IP每小时最多5次

### 速率限制响应
当触发速率限制时，返回429状态码：

```json
{
  "detail": "Rate limit exceeded for login. Please try again later."
}
```

## 🔧 配置说明

### 环境变量配置

| 变量名 | 描述 | 默认值 | 示例 |
|--------|------|--------|------|
| `USER_ACCESS_MODE` | 用户访问模式 | `OPEN` | `OPEN`, `LOGIN_ONLY`, `ADMIN_ONLY` |
| `REQUIRE_EMAIL_VERIFICATION` | 是否需要邮箱验证 | `false` | `true`, `false` |
| `ACCESS_TOKEN_EXPIRE_MINUTES` | 访问令牌过期时间(分钟) | `720` | `720` |
| `MIN_PASSWORD_LENGTH` | 最小密码长度 | `8` | `8` |
| `DEBUG_LEVEL` | 调试级别 | `0` | `0`, `1`, `2`, `3` |

### 访问模式详解

#### 模式 0: 完全开放 (OPEN)
- ✅ 允许用户注册
- ✅ 允许用户登录
- ✅ 允许管理员登录
- 适用场景: 公开服务，面向所有用户

#### 模式 1: 仅登录 (LOGIN_ONLY)
- ❌ 禁用用户注册
- ✅ 允许用户登录
- ✅ 允许管理员登录
- 适用场景: 内部系统，仅限已有用户

#### 模式 2: 管理员模式 (ADMIN_ONLY)
- ❌ 禁用用户注册
- ❌ 禁用用户登录
- ✅ 允许管理员登录
- 适用场景: 维护模式，仅管理员可访问

## 🔒 权限系统

### 用户角色
- **USER**: 普通用户，可访问基本功能
- **ADMIN**: 管理员，可管理用户和查看统计
- **SUPER_ADMIN**: 超级管理员，拥有所有权限

### 管理员权限
- **USER_VIEW**: 查看用户列表
- **USER_BAN**: 封禁/解封用户
- **ADMIN_MANAGE**: 管理其他管理员
- **SYSTEM_STATS**: 查看系统统计
- **SERVICE_CONTROL**: 控制服务状态

## 📝 使用示例

### 完整的用户注册登录流程

1. **发送注册验证码**
```bash
curl -X POST "http://localhost:8000/api/auth/send-register-code" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "email=<EMAIL>"
```

2. **用户注册（使用验证码）**
```bash
curl -X POST "http://localhost:8000/api/auth/register" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "email=<EMAIL>&password=password123&verification_code=123456&nickname=测试用户"
```

3. **用户登录**
```bash
curl -X POST "http://localhost:8000/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

4. **使用令牌访问受保护端点**
```bash
curl -X GET "http://localhost:8000/api/users/profile" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
```

### 管理员操作示例

1. **管理员登录**
```bash
curl -X POST "http://localhost:8000/api/admin/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "admin123"
  }'
```

2. **查看系统统计**
```bash
curl -X GET "http://localhost:8000/api/admin/stats" \
  -H "Authorization: Bearer <admin_token>"
```

3. **管理用户状态**
```bash
curl -X PUT "http://localhost:8000/api/admin/users/1/status" \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{"new_status": "BANNED"}'
```

## 🚀 快速开始

1. **启动应用**
```bash
python main.py
```

2. **访问API文档**
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

3. **健康检查**
```bash
curl http://localhost:8000/health
```

## 🏗️ 技术架构

### 核心技术栈
- **FastAPI** - 现代、快速的Web框架
- **asyncpg** - 高性能异步PostgreSQL驱动
- **Supabase** - 企业级PostgreSQL数据库
- **Vercel** - Serverless部署平台
- **SMTP** - 企业级邮件发送服务

### 架构优势

#### 🚀 性能优化
- **纯asyncpg连接** - 绕过SQLAlchemy ORM，直接使用原生SQL
- **无状态设计** - 完美适配Vercel serverless环境
- **连接池优化** - 支持70个并发数据库连接
- **零prepared statement冲突** - 完美兼容pgbouncer连接池

#### 🛡️ 安全特性
- **JWT令牌认证** - 无状态、安全的认证机制
- **邮箱验证机制** - 确保用户邮箱真实有效
- **密码哈希存储** - bcrypt加密，安全可靠
- **多层权限控制** - 用户、管理员、超级管理员三级权限

#### 🔧 解决的技术难题
1. **pgbouncer prepared statement冲突** - 使用statement_cache_size=0完全解决
2. **SQLAlchemy并发冲突** - 采用纯asyncpg架构绕过
3. **Vercel serverless限制** - 无状态设计，数据库持久化
4. **邮件服务集成** - 延迟导入避免模块加载冲突

### 部署信息
- **生产环境**: https://main.fxcores.com
- **数据库**: Supabase PostgreSQL
- **邮件服务**: SMTP (支持多种邮件提供商)
- **部署平台**: Vercel Serverless

## 📞 技术支持

- **API文档**: `/docs` 或 `/redoc`
- **健康检查**: `/health`
- **系统信息**: `/`

如有问题或建议，请联系开发团队。

---

*本文档基于 FastAuth_std v1.1.0 生成，最后更新时间: 2025年6月*