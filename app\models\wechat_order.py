"""
微信支付订单相关数据模型
"""

import enum
from datetime import datetime
from typing import TYPE_CHECKING, Optional

from sqlalchemy import DateTime, Enum, ForeignKey, Integer, String, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.core.datetime_utils import default_datetime
from app.db.database import Base

if TYPE_CHECKING:
    from .payment import PaymentRecord
    from .product import Product
    from .user import User


class WeChatTradeType(enum.Enum):
    """
    微信支付交易类型枚举
    """

    JSAPI = "JSAPI"  # 公众号支付
    NATIVE = "NATIVE"  # 扫码支付
    APP = "APP"  # APP支付
    MWEB = "MWEB"  # H5支付


class WeChatTradeState(enum.Enum):
    """
    微信支付交易状态枚举
    """

    SUCCESS = "SUCCESS"  # 支付成功
    REFUND = "REFUND"  # 转入退款
    NOTPAY = "NOTPAY"  # 未支付
    CLOSED = "CLOSED"  # 已关闭
    REVOKED = "REVOKED"  # 已撤销（付款码支付）
    USERPAYING = "USERPAYING"  # 用户支付中
    PAYERROR = "PAYERROR"  # 支付失败


class WeChatOrder(Base):
    """微信支付订单表"""

    __tablename__ = "wechat_orders"

    order_id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)

    # 关联字段
    user_id: Mapped[int] = mapped_column(
        Integer, ForeignKey("users.user_id"), nullable=False, index=True
    )
    product_id: Mapped[Optional[int]] = mapped_column(
        Integer, ForeignKey("products.product_id"), index=True
    )
    payment_record_id: Mapped[Optional[int]] = mapped_column(
        Integer, ForeignKey("payment_records.payment_id"), index=True
    )

    # 微信支付字段
    out_trade_no: Mapped[str] = mapped_column(
        String(32), unique=True, nullable=False, index=True
    )
    transaction_id: Mapped[Optional[str]] = mapped_column(String(64), index=True)
    prepay_id: Mapped[Optional[str]] = mapped_column(String(64))

    # 订单信息
    description: Mapped[str] = mapped_column(String(128), nullable=False)
    total: Mapped[int] = mapped_column(Integer, nullable=False)  # 金额，单位为分
    trade_type: Mapped[WeChatTradeType] = mapped_column(
        Enum(WeChatTradeType), nullable=False
    )
    trade_state: Mapped[WeChatTradeState] = mapped_column(
        Enum(WeChatTradeState), default=WeChatTradeState.NOTPAY
    )

    # 支付链接
    code_url: Mapped[Optional[str]] = mapped_column(Text)  # 扫码支付链接
    h5_url: Mapped[Optional[str]] = mapped_column(Text)  # H5支付链接

    # 用户信息（JSAPI支付需要）
    openid: Mapped[Optional[str]] = mapped_column(String(128))

    # 时间字段
    created_at: Mapped[datetime] = mapped_column(
        DateTime, default=default_datetime, index=True
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, default=default_datetime, onupdate=default_datetime
    )
    success_time: Mapped[Optional[datetime]] = mapped_column(DateTime)  # 支付成功时间

    # 回调信息
    notify_received: Mapped[bool] = mapped_column(
        Integer, default=False
    )  # 是否收到回调
    notify_time: Mapped[Optional[datetime]] = mapped_column(DateTime)  # 回调时间

    # 关系
    user: Mapped["User"] = relationship("User", back_populates="wechat_orders")
    product: Mapped[Optional["Product"]] = relationship(
        "Product", back_populates="wechat_orders"
    )
    payment_record: Mapped[Optional["PaymentRecord"]] = relationship(
        "PaymentRecord", back_populates="wechat_order"
    )


class WeChatRefund(Base):
    """微信支付退款记录表"""

    __tablename__ = "wechat_refunds"

    refund_id: Mapped[int] = mapped_column(
        Integer, primary_key=True, autoincrement=True
    )

    # 关联订单
    wechat_order_id: Mapped[int] = mapped_column(
        Integer, ForeignKey("wechat_orders.order_id"), nullable=False, index=True
    )

    # 退款信息
    out_refund_no: Mapped[str] = mapped_column(
        String(32), unique=True, nullable=False, index=True
    )
    refund_id_wx: Mapped[Optional[str]] = mapped_column(String(64))  # 微信退款单号

    # 金额信息
    refund_fee: Mapped[int] = mapped_column(
        Integer, nullable=False
    )  # 退款金额，单位为分
    total_fee: Mapped[int] = mapped_column(
        Integer, nullable=False
    )  # 原订单金额，单位为分

    # 退款原因和状态
    reason: Mapped[Optional[str]] = mapped_column(String(256))
    refund_status: Mapped[Optional[str]] = mapped_column(String(32))  # 退款状态

    # 时间字段
    created_at: Mapped[datetime] = mapped_column(DateTime, default=default_datetime)
    success_time: Mapped[Optional[datetime]] = mapped_column(DateTime)  # 退款成功时间

    # 关系
    wechat_order: Mapped["WeChatOrder"] = relationship("WeChatOrder")
