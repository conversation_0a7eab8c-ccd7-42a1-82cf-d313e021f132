"""
认证和安全相关功能
包含JWT令牌、密码哈希、权限验证等
"""

from datetime import datetime, timedelta, timezone
from typing import Optional, Union

from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from jose import JWTError, jwt
from passlib.context import CryptContext
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.db.database import get_db
from app.models.user import User

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# HTTP Bearer认证 - 完全禁用，只使用OAuth2PasswordBearer
# 不再在API文档中显示HTTPBearer认证方式
security = None  # 完全禁用HTTPBearer

# OAuth2 密码认证 - 支持用户和管理员登录
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl="/api/auth/token",  # 通用token端点
    auto_error=False,
)


class TokenData:
    """令牌数据类"""

    def __init__(self, username: Optional[str] = None, user_id: Optional[int] = None):
        self.username = username
        self.user_id = user_id


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """生成密码哈希"""
    return pwd_context.hash(password, rounds=settings.BCRYPT_ROUNDS)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(
        to_encode, settings.SECRET_KEY, algorithm=settings.JWT_ALGORITHM
    )
    return encoded_jwt


def verify_token(token: str) -> Optional[TokenData]:
    """验证令牌"""
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.JWT_ALGORITHM]
        )
        username = payload.get("sub")
        user_id = payload.get("user_id")

        if username is None:
            return None

        token_data = TokenData(username=username, user_id=user_id)
        return token_data
    except JWTError:
        return None


async def get_user_by_email(db: AsyncSession, email: str) -> Optional[User]:
    """根据邮箱获取用户"""
    result = await db.execute(select(User).where(User.email == email))
    return result.scalar_one_or_none()


async def get_user_by_id(db: AsyncSession, user_id: int) -> Optional[User]:
    """根据用户ID获取用户"""
    result = await db.execute(select(User).where(User.user_id == user_id))
    return result.scalar_one_or_none()


async def authenticate_user(
    db: AsyncSession, email: str, password: str
) -> Optional[User]:
    """认证用户"""
    user = await get_user_by_email(db, email)
    if not user:
        return None
    if not verify_password(password, user.password_hash):
        return None
    return user


async def get_current_user_from_token(
    token: str = Depends(oauth2_scheme), db: AsyncSession = Depends(get_db)
):
    """从OAuth2 token获取当前用户（支持用户名密码认证）"""
    if not token:
        return None

    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.JWT_ALGORITHM]
        )
        username = payload.get("sub")
        user_id = payload.get("user_id")
        role = payload.get("role")

        if username is None:
            raise credentials_exception

        # 检查是否为超级管理员
        if role == "super_admin":
            from app.models.user import UserRole, UserStatus

            return {
                "user_id": 0,
                "email": username,
                "role": UserRole.SUPER_ADMIN,
                "status": UserStatus.ACTIVE,
                "nickname": settings.SUPER_ADMIN_EMAIL.split("@")[0],
                "is_super_admin": True,
            }

        # 获取数据库用户
        if user_id:
            user = await get_user_by_id(db, user_id)
        else:
            user = await get_user_by_email(db, username)

        if user is None:
            raise credentials_exception

        return user

    except JWTError:
        raise credentials_exception


# HTTPBearer 认证函数已移除，只使用 OAuth2PasswordBearer 认证
# 所有用户认证现在通过 get_current_user_from_token 函数处理


async def get_current_active_user(
    current_user: Union[User, dict] = Depends(get_current_user_from_token),
):
    """获取当前活跃用户"""
    from app.models.user import UserStatus

    # 检查用户是否存在
    if current_user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # 处理超级管理员（字典格式）
    if isinstance(current_user, dict):
        if current_user.get("status") != UserStatus.ACTIVE:
            raise HTTPException(status_code=400, detail="User account is not active")
        return current_user

    # 处理数据库用户
    if current_user.status != UserStatus.ACTIVE:
        raise HTTPException(status_code=400, detail="User account is not active")
    return current_user


async def get_current_user_optional(
    current_user: Optional[Union[User, dict]] = Depends(get_current_user_from_token),
) -> Optional[Union[User, dict]]:
    """获取当前用户（可选认证）"""
    # 如果没有提供token或认证失败，返回None
    return current_user


async def get_current_active_user_optional(
    current_user: Optional[Union[User, dict]] = Depends(get_current_user_optional),
) -> Optional[Union[User, dict]]:
    """获取当前活跃用户（可选认证）"""
    if current_user is None:
        return None

    return await get_current_active_user(current_user)


def check_permissions(required_permissions: list[str]):
    """检查权限装饰器"""

    def permission_checker(current_user: User = Depends(get_current_active_user)):
        # 这里可以实现权限检查逻辑
        # 例如检查用户的角色和权限
        # 当前简化实现，只检查用户是否活跃
        return current_user

    return permission_checker


class RateLimiter:
    """简单的速率限制器"""

    def __init__(self):
        self.requests = {}

    def is_allowed(self, key: str, limit: int, window: int) -> bool:
        """检查是否允许请求"""
        now = datetime.now(timezone.utc)

        if key not in self.requests:
            self.requests[key] = []

        # 清理过期的请求记录
        self.requests[key] = [
            req_time
            for req_time in self.requests[key]
            if (now - req_time).total_seconds() < window
        ]

        # 检查是否超过限制
        if len(self.requests[key]) >= limit:
            return False

        # 记录当前请求
        self.requests[key].append(now)
        return True


# 全局速率限制器实例
rate_limiter = RateLimiter()


def rate_limit(limit: int = 100, window: int = 3600):
    """速率限制装饰器"""

    def rate_limit_checker(request):
        # 这里可以根据IP地址或用户ID进行限制
        client_ip = request.client.host

        if not rate_limiter.is_allowed(client_ip, limit, window):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded",
            )

        return True

    return rate_limit_checker
