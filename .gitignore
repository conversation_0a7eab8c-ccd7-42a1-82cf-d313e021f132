.idea
.ipynb_checkpoints
.mypy_cache
.vscode
__pycache__
.pytest_cache
htmlcov
dist
site
.coverage*
coverage.xml
.netlify
test.db
log.txt
Pipfile.lock
env3.*
env
docs_build
site_build
venv
docs.zip
archive.zip

# vim temporary files
*~
.*.sw?
.cache

# macOS
.DS_Store

# Vercel
.vercel
.env
.env.local
.env.production
.envback
.env.vercel

# 自定义过滤
#========================================================
scripts/
scripts/*.*
/docs
/static
/fast_pay
#========================================================
