"""
支付记录相关Pydantic模式定义
"""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict, Field

from app.schemas.common import PaymentType


# 支付记录相关模式
class PaymentRecordBase(BaseModel):
    """支付记录基础模式"""

    amount: float = Field(..., ge=0)
    payment_type: PaymentType
    product_id: Optional[int] = None
    order_id: Optional[str] = Field(None, max_length=50)
    payment_method: Optional[str] = Field(None, max_length=50)


class PaymentRecordCreate(PaymentRecordBase):
    """支付记录创建模式"""

    pass


class PaymentRecordResponse(PaymentRecordBase):
    """支付记录响应模式"""

    model_config = ConfigDict(from_attributes=True)

    payment_id: int
    user_id: int
    payment_date: datetime


# 微信支付相关操作模式
class WeChatOrderClose(BaseModel):
    """微信支付订单关闭模式"""

    out_trade_no: str = Field(..., max_length=32, description="商户订单号")


class WeChatRefundQuery(BaseModel):
    """微信支付退款查询模式"""

    out_refund_no: str = Field(..., max_length=32, description="商户退款单号")
