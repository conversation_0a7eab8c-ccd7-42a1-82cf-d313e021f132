# 依赖包说明文档

## 📋 文件结构
- `requirements.txt` - 生产环境依赖
- `requirements-dev.txt` - 开发环境依赖

## 🚀 生产环境依赖 (requirements.txt)

### FastAPI 核心框架
- `fastapi[standard]>=0.100.0` - FastAPI 主框架，包含标准功能

### 数据库相关
- `sqlalchemy>=2.0.0` - SQL 数据库 ORM 框架
- `asyncpg>=0.28.0` - PostgreSQL 异步驱动程序
- `psycopg2-binary>=2.9.0` - PostgreSQL 同步驱动程序（备用）
- `alembic>=1.12.0` - 数据库迁移工具

### Supabase 支持
- `supabase>=2.0.0` - Supabase 客户端库

### 认证和安全
- `python-jose[cryptography]>=3.3.0` - JWT 令牌处理
- `passlib[bcrypt]>=1.7.4` - 密码加密库
- `bcrypt>=3.2.0,<4.0.0` - 密码加密核心库（兼容 passlib 1.7.4）
- `python-multipart>=0.0.6` - 表单数据处理

### 配置管理
- `pydantic-settings>=2.0.0` - 设置和配置管理
- `python-dotenv>=1.0.0` - 环境变量文件支持

### 邮件支持
- `email-validator>=2.0.0` - 邮箱地址验证
- `aiosmtplib>=3.0.0` - 异步邮件发送

### HTTP 客户端
- `httpx>=0.24.0` - 现代异步 HTTP 客户端

### 类型检查
- `typing-extensions>=4.5.0` - 类型提示扩展

## 💻 开发环境依赖 (requirements-dev.txt)

### 开发和测试工具
- `pytest>=7.0.0` - 测试框架
- `pytest-asyncio>=0.21.0` - 异步测试支持
- `pytest-cov>=4.0.0` - 测试覆盖率

### 代码质量工具
- `ruff>=0.1.0` - 代码检查和格式化工具
- `pre-commit>=3.0.0` - Git 提交前检查
- `mypy>=1.8.0` - 静态类型检查

### 类型检查相关
- `types-ujson>=5.10.0` - ujson 类型定义
- `types-orjson>=3.6.2` - orjson 类型定义

### 文档生成工具
- `mkdocs>=1.5.0` - 文档生成器
- `mkdocs-material>=9.0.0` - Material 主题

### 其他开发工具
- `black>=24.0.0` - 代码格式化工具
- `coverage>=6.5.0` - 测试覆盖率分析
- `tomli>=2.0.0` - TOML 文件支持（Python < 3.11）

### 调试和性能分析
- `ipython>=8.0.0` - 交互式 Python 环境
- `rich>=13.0.0` - 美化输出格式

## 🔧 使用方法

### 生产环境安装
```bash
pip install -r requirements.txt
```

### 开发环境安装
```bash
pip install -r requirements-dev.txt
```

## 💡 注意事项

1. **bcrypt 版本锁定**：使用 `<4.0.0` 避免与 passlib 的兼容性问题
2. **开发环境自动包含生产环境**：requirements-dev.txt 通过 `-r requirements.txt` 引用
3. **编码问题**：requirements 文件使用英文注释避免编码问题
4. **版本管理**：使用 `>=` 确保最低版本要求
