"""
Dashboard API 数据模型定义
"""

from datetime import datetime
from typing import Dict, List, Optional
from pydantic import BaseModel, EmailStr, Field
from app.models.user import UserRole, UserStatus


# ========================================
# 用户管理相关模型
# ========================================

class DashboardUserResponse(BaseModel):
    """Dashboard用户响应模型"""
    
    user_id: int
    email: str
    nickname: Optional[str] = None
    status: str
    role: str
    register_date: Optional[str] = None
    last_login_date: Optional[str] = None
    email_verified: bool = False
    remarks: Optional[str] = None


class PaginationInfo(BaseModel):
    """分页信息"""
    
    total: int
    page: int
    size: int
    pages: int
    has_next: bool
    has_prev: bool


class FilterInfo(BaseModel):
    """过滤信息"""
    
    search: Optional[str] = None
    status: Optional[str] = None
    role: Optional[str] = None
    date_from: Optional[str] = None
    date_to: Optional[str] = None
    total_filtered: int


class DashboardUsersResponse(BaseModel):
    """Dashboard用户列表响应"""
    
    users: List[DashboardUserResponse]
    pagination: PaginationInfo
    filters_applied: FilterInfo


class UserCreateRequest(BaseModel):
    """用户创建请求"""
    
    email: EmailStr
    nickname: Optional[str] = Field(None, max_length=50)
    role: UserRole = UserRole.USER
    status: UserStatus = UserStatus.ACTIVE
    email_verified: bool = False
    remarks: Optional[str] = None


class UserCreateResponse(BaseModel):
    """用户创建响应"""
    
    success: bool
    message: str
    user: DashboardUserResponse
    temporary_password: str


class UserUpdateRequest(BaseModel):
    """用户完整更新请求"""
    
    email: Optional[EmailStr] = None
    nickname: Optional[str] = Field(None, max_length=50)
    status: Optional[UserStatus] = None
    role: Optional[UserRole] = None
    email_verified: Optional[bool] = None
    remarks: Optional[str] = None


class PasswordUpdateRequest(BaseModel):
    """密码更新请求"""
    
    new_password: str = Field(..., min_length=8, max_length=100)


class BatchStatusUpdateRequest(BaseModel):
    """批量状态更新请求"""
    
    user_ids: List[int] = Field(..., min_items=1, max_items=100)
    new_status: UserStatus


class BatchOperationResult(BaseModel):
    """批量操作结果"""
    
    success: bool
    message: str
    results: Dict


class UserStatsResponse(BaseModel):
    """用户统计响应"""
    
    total_users: int
    active_users: int
    banned_users: int
    pending_users: int
    verified_users: int
    unverified_users: int
    users_by_role: Dict[str, int]
    recent_registrations: Dict[str, int]


# ========================================
# 统一错误响应模型
# ========================================

class ErrorDetail(BaseModel):
    """错误详情"""
    
    code: str
    message: str
    details: Optional[Dict] = None


class ErrorResponse(BaseModel):
    """统一错误响应"""
    
    success: bool = False
    error: ErrorDetail
    timestamp: str


# ========================================
# 成功响应模型
# ========================================

class SuccessResponse(BaseModel):
    """通用成功响应"""

    success: bool = True
    message: str
    data: Optional[Dict] = None


# ========================================
# 用户详细信息相关模型
# ========================================

class UserKeyInfo(BaseModel):
    """用户密钥信息"""
    key_id: int
    key_string: str
    product_id: Optional[int]
    price: float
    start_date: str
    end_date: str
    activated_count: int
    max_activation_count: int
    status: str
    created_date: str


class UserPaymentInfo(BaseModel):
    """用户支付记录信息"""
    payment_id: int
    amount: float
    payment_type: str
    payment_date: str
    product_id: Optional[int]
    order_id: Optional[str]
    payment_method: Optional[str]


class UserWeChatOrderInfo(BaseModel):
    """用户微信订单信息"""
    order_id: int
    out_trade_no: str
    transaction_id: Optional[str]
    description: str
    total: int  # 分
    trade_type: str
    trade_state: str
    created_at: str
    success_time: Optional[str]
    notify_received: bool


class UserLogInfo(BaseModel):
    """用户日志信息"""
    log_id: int
    action: str
    action_date: str
    details: Optional[dict]


class UserStatsInfo(BaseModel):
    """用户统计信息"""
    total_keys: int
    active_keys: int
    expired_keys: int
    total_payments: float  # 总支付金额（元）
    successful_payments: int
    total_orders: int
    successful_orders: int
    total_logs: int
    last_activity_date: Optional[str]


class UserDetailedResponse(BaseModel):
    """用户详细信息响应（包含关联数据）"""
    # 基本用户信息
    user_id: int
    email: str
    nickname: Optional[str]
    status: str
    role: str
    register_date: Optional[str]
    last_login_date: Optional[str]
    email_verified: bool
    remarks: Optional[str]

    # 统计信息
    stats: UserStatsInfo

    # 关联数据（可选，根据需要加载）
    keys: Optional[List[UserKeyInfo]] = None
    payments: Optional[List[UserPaymentInfo]] = None
    wechat_orders: Optional[List[UserWeChatOrderInfo]] = None
    logs: Optional[List[UserLogInfo]] = None
