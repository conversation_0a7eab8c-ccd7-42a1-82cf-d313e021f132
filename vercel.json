{"functions": {"api/index.py": {"maxDuration": 30}}, "regions": ["hkg1"], "crons": [{"path": "/health", "schedule": "0 12 * * *"}], "rewrites": [{"source": "/health", "destination": "/api/index.py"}, {"source": "/(.*)", "destination": "/api/index.py"}], "env": {"PYTHONPATH": "."}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, HEAD, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}